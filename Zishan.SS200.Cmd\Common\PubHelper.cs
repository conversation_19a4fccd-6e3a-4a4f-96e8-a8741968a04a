﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using IniParser;
using IniParser.Model;
using Zishan.SS200.Cmd.Config;

namespace Zishan.SS200.Cmd.Common
{
    public static class PubHelper
    {
        /// <summary>
        /// 根据指定的 ini 配置文件路径获取配置信息。
        /// </summary>
        /// <param name="iniFilePath">ini 配置文件路径。</param>
        /// <returns>返回包含配置信息的 <see cref="IniConfig"/> 对象。</returns>
        public static IniConfig GetAppIniConfig(string iniFilePath)
        {
            var iniConfig = new IniConfig();
            var parser = new FileIniDataParser();

            IniData parsedData = parser.ReadFile(iniFilePath);

            //通过所有的段迭代
            //foreach (SectionData section in parsedData.Sections)
            //{
            //    Console.WriteLine("[" + section.SectionName + "]");

            //    //遍历当前节中的所有键以打印值
            //    foreach (KeyData key in section.Keys)
            //        Console.WriteLine(key.KeyName + " = " + key.Value);
            //}

            iniConfig.Ip = parsedData["General"]["Ip"];

            iniConfig.Port = Convert.ToInt32(parsedData["General"]["Port"]);

            iniConfig.AutoStart = Convert.ToBoolean(parsedData["General"]["AutoStart"]);

            // 读取UI日志配置
            if (parsedData["General"]["ShowCallerInfoInUILog"] != null)
            {
                iniConfig.ShowCallerInfoInUILog = Convert.ToBoolean(parsedData["General"]["ShowCallerInfoInUILog"]);
            }

            // 读取UI界面日志显示配置
            if (parsedData["General"]["ShowUILog"] != null)
            {
                iniConfig.ShowUILog = Convert.ToBoolean(parsedData["General"]["ShowUILog"]);
            }

            // 读取设备特定配置
            iniConfig.ShuttleIp = parsedData["Devices"]["ShuttleIp"];
            iniConfig.ShuttlePort = Convert.ToInt32(parsedData["Devices"]["ShuttlePort"]);

            iniConfig.RobotIp = parsedData["Devices"]["RobotIp"];
            iniConfig.RobotPort = Convert.ToInt32(parsedData["Devices"]["RobotPort"]);

            iniConfig.ChaIp = parsedData["Devices"]["ChaIp"];
            iniConfig.ChaPort = Convert.ToInt32(parsedData["Devices"]["ChaPort"]);

            iniConfig.ChbIp = parsedData["Devices"]["ChbIp"];
            iniConfig.ChbPort = Convert.ToInt32(parsedData["Devices"]["ChbPort"]);

            iniConfig.ZAxixMinSafeValue = Convert.ToInt32(parsedData["Devices"]["ZAxixMinSafeValue"]);

            return iniConfig;
        }

        /// <summary>
        /// 根据输入指定路径的文件匹配规则，获取一个唯一可用规则，获取配方列表，例如：C:\IR400\*.dev，返回包含相应配方名称的列表。
        /// </summary>
        /// <param name="path">文件路径，例如 C:\IR400\</param>
        /// <param name="searchPattern">文件扩展名，例如 *.dev</param>
        /// <returns>包含相应配方名称的列表。</returns>
        /// <exception cref="ApplicationException">当指定目录未找到或访问文件时发生错误时抛出。</exception>
        /// <exception cref="InvalidOperationException">当找到多个有效的配方文件名时抛出。</exception>
        public static List<string> GetRecipeList(string path, string searchPattern)
        {
            var recipes = new List<string>();

            try
            {
                var files = Directory.GetFiles(path, searchPattern);
                var lstFileNameWithoutExt = new List<string>();

                foreach (var file in files)
                {
                    if (File.Exists(file))
                    {
                        var fileNameWithoutExt = Path.GetFileNameWithoutExtension(file);

                        // 检查文件名是否只包含字母A、B、C，并且是正序的
                        if (IsValidRecipeName(fileNameWithoutExt))
                        {
                            lstFileNameWithoutExt.Add(fileNameWithoutExt);
                        }
                    }
                }

                if (lstFileNameWithoutExt.Count > 1)
                {
                    var filePaths = files.Where(file => lstFileNameWithoutExt.Contains(Path.GetFileNameWithoutExtension(file)));
                    AppLog.Error("找到多个有效的配方文件名: " + string.Join(", ", filePaths));
                }

                if (lstFileNameWithoutExt.Count == 1)
                {
                    recipes.AddRange(GetRecipesFromName(lstFileNameWithoutExt[0]));
                }

                return recipes.Distinct().ToList();
            }
            catch (DirectoryNotFoundException ex)
            {
                AppLog.Error("目录未找到: " + path, ex);
            }
            catch (IOException ex)
            {
                AppLog.Error("文件访问错误: " + path, ex);
            }
            catch (InvalidOperationException ex)
            {
                AppLog.Error("配方文件名错误: " + path, ex);
            }
            catch (Exception ex)
            {
                AppLog.Error("未知错误: " + path, ex);
            }

            return recipes;
        }

        /// <summary>
        /// 检查文件名是否只包含字母A、B、C，并且是正序的。
        /// </summary>
        /// <param name="name">文件名。</param>
        /// <returns>如果文件名只包含字母A、B、C，并且是正序的，则返回 true；否则返回 false。</returns>
        private static bool IsValidRecipeName(string name)
        {
            // 检查是否只包含A、B、C
            if (name.Except("ABC").Any())
            {
                return false;
            }

            // 检查是否是正序
            var sortedName = new string(name.OrderBy(c => c).ToArray());
            return name == sortedName;
        }

        /// <summary>
        /// 根据文件名获取配方列表。
        /// </summary>
        /// <param name="name">文件名。</param>
        /// <returns>包含相应配方名称的列表。</returns>
        private static List<string> GetRecipesFromName(string name)
        {
            var recipes = new List<string>();

            if (name.Contains("A"))
            {
                recipes.Add("配方A");
            }
            if (name.Contains("B"))
            {
                recipes.Add("配方B");
            }
            if (name.Contains("C"))
            {
                recipes.Add("配方C");
            }
            if (name.Contains("A") && name.Contains("B"))
            {
                recipes.Add("配方AB");
            }
            if (name.Contains("A") && name.Contains("C"))
            {
                recipes.Add("配方AC");
            }
            if (name.Contains("B") && name.Contains("C"))
            {
                recipes.Add("配方BC");
            }
            if (name.Contains("A") && name.Contains("B") && name.Contains("C"))
            {
                recipes.Add("配方ABC");
            }

            return recipes;
        }
    }
}