﻿using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using log4net;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Models;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.ViewModels.Dock;
using Zishan.SS200.Cmd.Constants.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;

namespace Zishan.SS200.Cmd.ViewModels
{
    /// <summary>
    /// 晶圆搬运测试视图模型
    /// </summary>
    public partial class BasicCommandTestViewModel : ObservableObject
    {
        #region 字段

        private readonly ILog _logger = LogManager.GetLogger(typeof(BasicCommandTestViewModel));
        private readonly IS200McuCmdService _mcuCmdService;
        private string _strMsg;

        /// <summary>
        /// 取消令牌源，用于取消测试操作
        /// </summary>
        private CancellationTokenSource _cancellationTokenSource = new();

        /// <summary>
        /// 集合同步锁对象
        /// </summary>
        private readonly object _logListLock = new object();

        /// <summary>
        /// 实时状态表互锁实例，用于获取Robot实时状态
        /// </summary>
        private readonly SS200InterLockMain _interlock = SS200InterLockMain.Instance;

        #endregion 字段

        #region 属性

        /// <summary>
        /// R轴是否已归零状态 - 优先使用实时状态，本地状态作为备用
        /// </summary>
        [ObservableProperty]
        private bool isRAxisAtZero = false;

        /// <summary>
        /// 当前T轴的位置类型
        /// </summary>
        [ObservableProperty]
        private EnuLocationStationType currentTAxisLocation = EnuLocationStationType.Cassette;

        /// <summary>
        /// 当前操作的机械臂端口类型
        /// </summary>
        [ObservableProperty]
        private EnuRobotEndType currentActiveEndType = EnuRobotEndType.Nose;

        // 是否启用安全模式（强制执行轴操作顺序检查）
        [ObservableProperty]
        private bool isSafetyModeEnabled = true;

        /// <summary>
        /// 循环执行次数：-1代表无限循环，默认1执行一次
        /// </summary>
        [ObservableProperty]
        private int loopCount = 1;

        /// <summary>
        /// 剩余循环次数：-1代表无限循环，0表示循环结束
        /// </summary>
        [ObservableProperty]
        private int remainingLoopCount = 1;

        /// <summary>
        /// 已执行次数：Pin Search测试和搬运按钮共用的累计计数器
        /// </summary>
        [ObservableProperty]
        private int executedCount = 0;

        /// <summary>
        /// 是否已经开始执行测试：用于区分"未开始"和"已执行: 0次"状态
        /// </summary>
        [ObservableProperty]
        private bool hasStartedExecution = false;

        /// <summary>
        /// 当LoopCount属性变化时，同步更新RemainingLoopCount
        /// </summary>
        partial void OnLoopCountChanged(int value)
        {
            // 简单修正：除了-1之外的负数都改为1
            if (value < -1)
            {
                LoopCount = 1;
                UILogService.AddWarningLog($"循环次数已修正：{value} -> 1");
                return;
            }

            // 只有在非执行状态下才同步更新剩余次数
            if (!IsExecutingCommand)
            {
                RemainingLoopCount = value;
            }
        }

        /// <summary>
        /// 停止循环执行命令
        /// </summary>
        [RelayCommand]
        private void StopLoop()
        {
            try
            {
                if (_cancellationTokenSource != null && !_cancellationTokenSource.Token.IsCancellationRequested)
                {
                    _cancellationTokenSource.Cancel();
                    UILogService.AddWarningLog("用户请求停止循环执行");
                    HcGrowlExtensions.Warning("正在停止循环执行...", waitTime: 2);

                    // 重新创建取消令牌源以供下次使用
                    _cancellationTokenSource?.Dispose();
                    _cancellationTokenSource = new();
                }
                else
                {
                    UILogService.AddLog("当前没有正在执行的循环操作");
                    HcGrowlExtensions.Info("当前没有正在执行的循环操作", waitTime: 2);
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"停止循环执行异常: {ex.Message}");
                _logger.Error($"停止循环执行异常", ex);
            }
        }

        /// <summary>
        /// 紧急停止电机命令 - 直接调用底层Modbus命令停止T、R、Z三轴电机【备注：命令需要发送其它2、3命令区，目前暂未实现】
        /// </summary>
        [RelayCommand]
        private async Task EmergencyStop()
        {
            try
            {
                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddErrorLog("Robot未连接，无法执行紧急停止命令");
                    HcGrowlExtensions.Error("Robot未连接，无法执行紧急停止命令");
                    return;
                }

                UILogService.AddWarningLog("执行紧急停止命令 - 停止所有轴电机");
                HcGrowlExtensions.Warning("正在执行紧急停止...", waitTime: 3);

                // 设置执行状态
                bool wasExecuting = IsExecutingCommand;
                IsExecutingCommand = true;

                try
                {
                    // 依次停止T轴、R轴、Z轴电机
                    // 1: T轴, 2: R轴, 3: Z轴
                    string[] axisNames = { "T轴", "R轴", "Z轴" };

                    for (int axis = 1; axis <= 3; axis++)
                    {
                        UILogService.AddLog($"正在停止{axisNames[axis - 1]}电机...");

                        var result = await _mcuCmdService.Robot.Run(
                            EnuRobotCmdIndex.MotorStop,
                            new List<ushort> { (ushort)axis }
                        );

                        if (result.ReturnInfo == 0)
                        {
                            UILogService.AddLog($"✅ {axisNames[axis - 1]}电机停止成功");
                        }
                        else
                        {
                            UILogService.AddErrorLog($"❌ {axisNames[axis - 1]}电机停止失败: {result.Response}");
                        }
                    }

                    UILogService.AddWarningLog("紧急停止命令执行完成");
                    HcGrowlExtensions.Success("紧急停止命令执行完成", waitTime: 2);
                }
                finally
                {
                    // 恢复执行状态
                    IsExecutingCommand = wasExecuting;
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"紧急停止命令执行异常: {ex.Message}");
                HcGrowlExtensions.Error($"紧急停止命令执行异常: {ex.Message}");
                _logger.Error($"紧急停止命令执行异常", ex);
            }
        }

        /// <summary>
        /// 版本
        /// </summary>
        [ObservableProperty]
        private string title = "搬运测试";

        /// <summary>
        /// From单元位置选项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LocationStation> fromChamber = new ObservableCollection<LocationStation>();

        /// <summary>
        /// From单元位置的可用晶圆SLOT集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<WaferSlot> fromAvailableWafers = new ObservableCollection<WaferSlot>();

        /// <summary>
        /// To单元位置选项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LocationStation> toChamber = new ObservableCollection<LocationStation>();

        /// <summary>
        /// To单元位置的可用晶圆SLOT集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<WaferSlot> toAvailableWafers = new ObservableCollection<WaferSlot>();

        /// <summary>
        /// 机械臂端口类型选项集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<string> byArmFetchSide = new ObservableCollection<string>();

        /// <summary>
        /// 选中的From单元位置
        /// </summary>
        [ObservableProperty]
        private LocationStation selectedFromChamber;

        /*/// <summary>
        /// 临时添加，后面会移除  Robot命令轴分类整理：1、轴分类；2、Smooth、Nose端分类；3、最后选择具体的腔体，比如：ChamberA、ChamberB、Cooling
        /// </summary>
        public ObservableCollection<string> RobotCmdList { get; set; } = new() { "AR1", "AR2", "AR3" };

        [ObservableProperty]
        public EnuRobotCmd selectedRobotCmd;*/

        /// <summary>
        /// 选中的From SLOT号
        /// </summary>
        [ObservableProperty]
        private int selectedFromSlot;

        /// <summary>
        /// 选中的From SLOT号变更处理
        /// </summary>
        partial void OnSelectedFromSlotChanged(int value)
        {
            // 处理空值或无效值情况
            if (value <= 0 && FromAvailableWafers.Count > 0)
            {
                // 选择第一个有效的SLOT
                SelectedFromSlot = FromAvailableWafers[0].WaferNo;
            }
        }

        /// <summary>
        /// 选中的To单元位置
        /// </summary>
        [ObservableProperty]
        private LocationStation selectedToChamber;

        /// <summary>
        /// 选中的To SLOT号
        /// </summary>
        [ObservableProperty]
        private int selectedToSlot;

        /// <summary>
        /// 选中的To SLOT号变更处理
        /// </summary>
        partial void OnSelectedToSlotChanged(int value)
        {
            // 处理空值或无效值情况
            if (value <= 0 && ToAvailableWafers.Count > 0)
            {
                // 选择第一个有效的SLOT
                SelectedToSlot = ToAvailableWafers[0].WaferNo;
            }
        }

        /// <summary>
        /// 选中的机械臂端口类型
        /// </summary>
        [ObservableProperty]
        private string selectedByArmFetchSide;

        /// <summary>
        /// 是否正在执行命令
        /// </summary>
        [ObservableProperty]
        private bool isExecutingCommand;

        /// <summary>
        /// 命令执行结果
        /// </summary>
        [ObservableProperty]
        private string commandResult;

        /// <summary>
        /// Robot命令执行结果
        /// </summary>
        [ObservableProperty]
        private string cmdResult;

        /// <summary>
        /// 获取Robot连接状态
        /// </summary>
        public bool IsRobotConnected => _mcuCmdService?.Robot?.IsConnected ?? false;

        /// <summary>
        /// 当前测试状态
        /// </summary>
        [ObservableProperty]
        private bool isTestRunning;

        /// <summary>
        /// 位置类型集合
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<LocationStation> locationTypes = new ObservableCollection<LocationStation>();

        /// <summary>
        /// 选中的位置类型
        /// </summary>
        [ObservableProperty]
        private LocationStation selectedLocationType;

        /// <summary>
        /// Z轴位置值
        /// </summary>
        [ObservableProperty]
        private string zAxisPosition = "0";

        /// <summary>
        /// T轴位置值
        /// </summary>
        [ObservableProperty]
        private string tAxisPosition = "0";

        /// <summary>
        /// R轴位置值
        /// </summary>
        [ObservableProperty]
        private string rAxisPosition = "0";

        /// <summary>
        /// 选中的Slot号
        /// </summary>
        [ObservableProperty]
        private string slotNumber = "1";

        /// <summary>
        /// 获取当前RTZ位置命令
        /// </summary>
        [RelayCommand]
        private void GetCurrentRTZPosition()
        {
            try
            {
                var rtzPosition = _interlock.RTZAxisPosition;

                // 检查数据有效性
                if (!rtzPosition.IsRTZPositionDataValid)
                {
                    UILogService.AddWarningLog("RTZ轴位置数据无效，请确保Robot设备已连接并启动了报警监控");
                    return;
                }

                // 获取当前RTZ轴位置并更新到UI
                TAxisPosition = rtzPosition.CurrentTAxisStep.ToString();
                RAxisPosition = rtzPosition.CurrentRAxisStep.ToString();
                ZAxisPosition = rtzPosition.CurrentZAxisStep.ToString();

                // 记录日志
                UILogService.AddSuccessLog($"已获取当前RTZ位置: T={TAxisPosition}, R={RAxisPosition}, Z={ZAxisPosition}");

                // 显示物理单位信息
                string physicalInfo = $"物理单位: T轴={rtzPosition.CurrentTAxisDegree:F2}°, R轴={rtzPosition.CurrentRAxisLength:F2}mm, Z轴={rtzPosition.CurrentZAxisHeight:F2}mm";
                UILogService.AddLog(physicalInfo);
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"获取当前RTZ位置失败: {ex.Message}");
                _logger.Error($"获取当前RTZ位置失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Robot命令列表
        /// </summary>
        public ObservableCollection<EnuRobotCmd> RobotCommandList { get; set; } = new();

        /// <summary>
        /// 选中的Robot命令
        /// </summary>
        [ObservableProperty]
        private EnuRobotCmd selectedRobotCommand = EnuRobotCmd.AR1;

        /// <summary>
        /// 是否允许TRZ轴归零操作，主要针对搬运、PIN Search等操作前的归零检查，默认为false，表示不允许归零操作
        /// </summary>
        [ObservableProperty]
        private bool isTRZAxisReturnZeroed = false;

        #endregion 属性

        #region 内部使用类

        /// <summary>
        /// 位置站点模型类
        /// </summary>
        public class LocationStation
        {
            /// <summary>
            /// 站点类型枚举
            /// </summary>
            public EnuLocationStationType StationType { get; set; }

            /// <summary>
            /// 站点显示名称
            /// </summary>
            public string ChamberName { get; set; }

            /// <summary>
            /// 显示名称
            /// </summary>
            public string DisplayName { get; set; }

            /// <summary>
            /// 是否可用
            /// </summary>
            public bool IsEnabled { get; set; } = true;

            /// <summary>
            /// 描述信息
            /// </summary>
            public string Description { get; set; }

            public override string ToString()
            {
                return DisplayName ?? ChamberName;
            }
        }

        /// <summary>
        /// 晶圆SLOT模型
        /// </summary>
        public class WaferSlot
        {
            /// <summary>
            /// SLOT号
            /// </summary>
            public int WaferNo { get; set; }

            /// <summary>
            /// 是否有晶圆
            /// </summary>
            public bool HasWafer { get; set; }

            /// <summary>
            /// 是否可用
            /// </summary>
            public bool IsEnabled { get; set; } = true;

            /// <summary>
            /// 描述信息
            /// </summary>
            public string Description { get; set; }

            public override string ToString()
            {
                return WaferNo.ToString();
            }
        }

        #endregion 内部使用类

        #region 构造函数

        public BasicCommandTestViewModel() : this(new S200McuCmdService())
        {
        }

        /// <summary>
        /// 构造函数（带依赖注入）
        /// </summary>
        public BasicCommandTestViewModel(IS200McuCmdService mcuCmdService)
        {
            _mcuCmdService = mcuCmdService ?? throw new ArgumentNullException(nameof(mcuCmdService));
            _logger.Info($"BasicCommandTestViewModel 已创建，Robot连接状态: {_mcuCmdService.Robot.IsConnected}");

            InitializeViewModel();

            // 延迟初始化R轴状态，等待实时状态表准备就绪
            _ = Task.Run(async () =>
            {
                // 智能等待实时状态表准备就绪
                int maxRetries = 10;
                int retryCount = 0;

                while (retryCount < maxRetries && !IsRealTimeStatusReady())//IsRealTimeStatusReady 会始终返回True，使用这个方法来检查实时状态表是否有数据，不正确
                {
                    await Task.Delay(1000); // 每500ms检查一次
                    retryCount++;
                }

                await Task.Delay(2000); //再次延迟
                // 在UI线程上更新状态
                Application.Current?.Dispatcher.Invoke(() =>
                {
                    if (IsRealTimeStatusReady())
                    {
                        UpdateRAxisZeroStatus();
                        _logger.Info("R轴状态已通过实时状态表初始化");
                    }
                    else
                    {
                        _logger.Warn("实时状态表未准备就绪，使用默认R轴状态");
                        // 保持默认的false状态
                    }
                });
            });
        }

        #endregion 构造函数

        #region R轴状态管理

        /// <summary>
        /// 更新R轴零位状态 - 使用混合方式确保准确性
        /// </summary>
        private void UpdateRAxisZeroStatus()
        {
            try
            {
                // 优先使用实时状态表的计算结果
                var robotStatus = _interlock.SubsystemStatus.Robot.Status;
                if (robotStatus != null)
                {
                    // 如果实时状态表有数据，使用实时计算的结果
                    IsRAxisAtZero = robotStatus.RAxisIsZeroPosition;//这边需要靠赋值才更新，怎么robotStatus.RAxisIsZeroPosition状态改变，立马通知IsRAxisAtZero
                    _logger.Debug($"使用实时状态表更新R轴零位状态: {IsRAxisAtZero}");
                }
                else
                {
                    // 如果实时状态表暂时没有数据，保持当前本地状态
                    _logger.Debug($"实时状态表暂无数据，保持本地R轴零位状态: {IsRAxisAtZero}");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"更新R轴零位状态失败: {ex.Message}");
                // 发生异常时保持当前状态不变
            }
        }

        /// <summary>
        /// 检查实时状态表是否已准备就绪并有有效数据
        /// </summary>
        private bool IsRealTimeStatusReady()
        {
            try
            {
                // 检查Robot连接状态
                if (_mcuCmdService?.Robot?.IsConnected != true)
                {
                    return false;
                }

                // 检查实时状态表对象是否存在
                var robotStatus = _interlock?.SubsystemStatus?.Robot?.Status;
                if (robotStatus == null)
                {
                    return false;
                }

                // 检查是否有Robot报警寄存器数据（表示通信正常）
                var robotAlarmRegisters = _mcuCmdService.RobotAlarmRegisters;
                if (robotAlarmRegisters == null || robotAlarmRegisters.Count == 0)
                {
                    return false;
                }

                // 检查Robot线圈数据是否存在（表示I/O通信正常）
                var robotCoils = _mcuCmdService.RobotCoils;
                if (robotCoils == null || robotCoils.Count == 0)
                {
                    return false;
                }

                // 如果所有检查都通过，认为实时状态表已准备就绪
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取当前R轴零位状态 - 智能选择最可靠的数据源
        /// </summary>
        /// <returns>R轴是否在零位</returns>
        public bool GetCurrentRAxisZeroStatus()
        {
            try
            {
                // 优先使用实时状态表的计算结果
                var robotStatus = _interlock.SubsystemStatus.Robot.Status;
                if (robotStatus != null)
                {
                    // 更新本地状态以保持同步
                    IsRAxisAtZero = robotStatus.RAxisIsZeroPosition;
                    return robotStatus.RAxisIsZeroPosition;
                }

                // 如果实时状态表不可用，使用本地状态
                _logger.Debug("实时状态表不可用，使用本地R轴零位状态");
                return IsRAxisAtZero;
            }
            catch (Exception ex)
            {
                _logger.Error($"获取R轴零位状态失败: {ex.Message}");
                // 发生异常时返回本地状态
                return IsRAxisAtZero;
            }
        }

        #endregion R轴状态管理

        #region 方法

        /// <summary>
        /// 初始化视图模型
        /// </summary>
        private void InitializeViewModel()
        {
            try
            {
                // 初始化From单元位置选项
                InitializeFromChamberOptions();

                // 初始化To单元位置选项
                InitializeToChamberOptions();

                // 初始化机械臂端口类型选项
                InitializeArmEndTypeOptions();

                // 初始化位置类型选项
                InitializeLocationTypeOptions();

                // 初始化Robot命令列表
                InitializeRobotCommandList();

                // 设置默认选中项
                if (FromChamber.Count > 0)
                    SelectedFromChamber = FromChamber.FirstOrDefault(t => t.StationType == EnuLocationStationType.CoolingTop);

                if (ToChamber.Count > 0)
                    SelectedToChamber = ToChamber.FirstOrDefault(t => t.StationType == EnuLocationStationType.ChamberB);

                if (LocationTypes.Count > 0)
                    SelectedLocationType = LocationTypes.FirstOrDefault(t => t.StationType == EnuLocationStationType.Cassette);

                // 选中项变更处理
                OnSelectedFromChamberChanged(SelectedFromChamber);
                OnSelectedToChamberChanged(SelectedToChamber);
            }
            catch (Exception ex)
            {
                _logger.Error($"初始化晶圆搬运视图模型异常: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 初始化From单元位置选项
        /// </summary>
        private void InitializeFromChamberOptions()
        {
            FromChamber.Clear();

            // 添加所有可能的站点位置
            FromChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.Cassette,
                ChamberName = "Cassette",
                Description = "Cassette位置"
            });

            FromChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.ChamberA,
                ChamberName = "ChamberA",
                Description = "ChamberA位置"
            });

            FromChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.ChamberB,
                ChamberName = "ChamberB",
                Description = "ChamberB位置"
            });

            FromChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.CoolingTop,
                ChamberName = "CoolingTop",
                Description = "冷却腔上层位置"
            });

            FromChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.CoolingBottom,
                ChamberName = "CoolingBottom",
                Description = "冷却腔下层位置"
            });
        }

        /// <summary>
        /// 初始化To单元位置选项
        /// </summary>
        private void InitializeToChamberOptions()
        {
            ToChamber.Clear();

            // 添加所有可能的站点位置
            ToChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.Cassette,
                ChamberName = "Cassette",
                Description = "Cassette位置"
            });

            ToChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.ChamberA,
                ChamberName = "ChamberA",
                Description = "ChamberA位置"
            });

            ToChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.ChamberB,
                ChamberName = "ChamberB",
                Description = "ChamberB位置"
            });

            ToChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.CoolingTop,
                ChamberName = "CoolingTop",
                Description = "冷却腔上层位置"
            });

            ToChamber.Add(new LocationStation
            {
                StationType = EnuLocationStationType.CoolingBottom,
                ChamberName = "CoolingBottom",
                Description = "冷却腔下层位置"
            });
        }

        /// <summary>
        /// 初始化机械臂端口类型选项
        /// </summary>
        private void InitializeArmEndTypeOptions()
        {
            ByArmFetchSide.Clear();

            // 添加所有可能的机械臂端口类型
            ByArmFetchSide.Add("Nose端");
            ByArmFetchSide.Add("Smooth端");

            // 设置默认选中项
            SelectedByArmFetchSide = ByArmFetchSide.FirstOrDefault();
        }

        /// <summary>
        /// 初始化位置类型选项
        /// </summary>
        private void InitializeLocationTypeOptions()
        {
            LocationTypes.Clear();

            // 添加所有可能的站点位置
            LocationTypes.Add(new LocationStation
            {
                StationType = EnuLocationStationType.Cassette,
                DisplayName = "Cassette",
                Description = "Cassette位置"
            });

            LocationTypes.Add(new LocationStation
            {
                StationType = EnuLocationStationType.ChamberA,
                DisplayName = "ChamberA",
                Description = "ChamberA位置"
            });

            LocationTypes.Add(new LocationStation
            {
                StationType = EnuLocationStationType.ChamberB,
                DisplayName = "ChamberB",
                Description = "ChamberB位置"
            });

            LocationTypes.Add(new LocationStation
            {
                StationType = EnuLocationStationType.CoolingTop,
                DisplayName = "CoolingTop",
                Description = "CoolingTop位置"
            });

            LocationTypes.Add(new LocationStation
            {
                StationType = EnuLocationStationType.CoolingBottom,
                DisplayName = "CoolingBottom",
                Description = "CoolingBottom位置"
            });
        }

        /// <summary>
        /// 初始化Robot命令列表
        /// </summary>
        private void InitializeRobotCommandList()
        {
            RobotCommandList.Clear();

            // 获取EnuRobotCmd枚举的所有值并添加到列表中
            foreach (EnuRobotCmd cmd in Enum.GetValues(typeof(EnuRobotCmd)))
            {
                RobotCommandList.Add(cmd);
            }
        }

        /// <summary>
        /// 选中的From单元位置变更处理
        /// </summary>
        partial void OnSelectedFromChamberChanged(LocationStation value)
        {
            try
            {
                _logger.Debug($"选中的From单元位置变更: {value?.ChamberName ?? "null"}");

                if (value == null)
                {
                    _logger.Warn("选中的From单元位置为null");
                    UILogService.AddLog("警告: 选中的From单元位置为null");
                    return;
                }

                // 根据选中的位置更新可用的SLOT
                UpdateFromAvailableWafers(value.StationType);
            }
            catch (Exception ex)
            {
                _logger.Error($"处理From单元位置变更异常: {ex.Message}", ex);
                UILogService.AddLog($"处理From单元位置变更异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 选中的To单元位置变更处理
        /// </summary>
        partial void OnSelectedToChamberChanged(LocationStation value)
        {
            try
            {
                _logger.Debug($"选中的To单元位置变更: {value?.ChamberName ?? "null"}");

                if (value == null)
                {
                    _logger.Warn("选中的To单元位置为null");
                    UILogService.AddLog("警告: 选中的To单元位置为null");
                    return;
                }

                // 根据选中的位置更新可用的SLOT
                UpdateToAvailableWafers(value.StationType);
            }
            catch (Exception ex)
            {
                _logger.Error($"处理To单元位置变更异常: {ex.Message}", ex);
                UILogService.AddLog($"处理To单元位置变更异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新From位置可用的SLOT
        /// </summary>
        private void UpdateFromAvailableWafers(EnuLocationStationType stationType)
        {
            try
            {
                // 记录操作开始
                _logger.Debug($"更新From位置可用的SLOT开始: 站点类型={stationType}");

                // 清空之前的数据
                FromAvailableWafers.Clear();

                // 根据站点类型生成不同的SLOT选项
                int slotCount = GetSlotCountByStationType(stationType);
                _logger.Debug($"From位置SLOT数量: {slotCount}");

                // 先填充集合
                for (int i = 1; i <= slotCount; i++)
                {
                    FromAvailableWafers.Add(new WaferSlot
                    {
                        WaferNo = i,
                        HasWafer = true,  // 默认有晶圆
                        Description = $"SLOT {i}"
                    });
                }

                // 设置默认选中SLOT
                if (FromAvailableWafers.Count > 0)
                {
                    int defaultSlot = FromAvailableWafers[0].WaferNo;

                    // 确保在界面更新完成后设置选中项
                    Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        try
                        {
                            SelectedFromSlot = defaultSlot;
                            _logger.Debug($"设置From默认选中SLOT: {SelectedFromSlot}");
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"设置From默认选中SLOT异常: {ex.Message}", ex);
                            UILogService.AddLog($"设置From默认选中SLOT异常: {ex.Message}");
                        }
                    });
                }
                else
                {
                    _logger.Warn("From位置没有可用的SLOT");
                    UILogService.AddLog("警告: From位置没有可用的SLOT");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"更新From位置可用的SLOT异常: {ex.Message}", ex);
                UILogService.AddLog($"更新From位置可用的SLOT异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 更新To位置可用的SLOT
        /// </summary>
        private void UpdateToAvailableWafers(EnuLocationStationType stationType)
        {
            try
            {
                // 记录操作开始
                _logger.Debug($"更新To位置可用的SLOT开始: 站点类型={stationType}");

                // 清空之前的数据
                ToAvailableWafers.Clear();

                // 根据站点类型生成不同的SLOT选项
                int slotCount = GetSlotCountByStationType(stationType);
                _logger.Debug($"To位置SLOT数量: {slotCount}");

                // 先填充集合
                for (int i = 1; i <= slotCount; i++)
                {
                    ToAvailableWafers.Add(new WaferSlot
                    {
                        WaferNo = i,
                        HasWafer = false,  // 默认无晶圆
                        Description = $"SLOT {i}"
                    });
                }

                // 设置默认选中SLOT
                if (ToAvailableWafers.Count > 0)
                {
                    int defaultSlot = ToAvailableWafers[0].WaferNo;

                    // 确保在界面更新完成后设置选中项
                    Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        try
                        {
                            SelectedToSlot = defaultSlot;
                            _logger.Debug($"设置To默认选中SLOT: {SelectedToSlot}");
                        }
                        catch (Exception ex)
                        {
                            _logger.Error($"设置To默认选中SLOT异常: {ex.Message}", ex);
                            UILogService.AddLog($"设置To默认选中SLOT异常: {ex.Message}");
                        }
                    });
                }
                else
                {
                    _logger.Warn("To位置没有可用的SLOT");
                    UILogService.AddLog("警告: To位置没有可用的SLOT");
                }
            }
            catch (Exception ex)
            {
                _logger.Error($"更新To位置可用的SLOT异常: {ex.Message}", ex);
                UILogService.AddLog($"更新To位置可用的SLOT异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据站点类型获取SLOT数量
        /// </summary>
        private int GetSlotCountByStationType(EnuLocationStationType stationType)
        {
            return stationType switch
            {
                EnuLocationStationType.Cassette => Golbal.CassetteSlotMax,      // Cassette有25个SLOT
                EnuLocationStationType.ChamberA => Golbal.CassetteSlotMin,       // ChamberA只有1个SLOT
                EnuLocationStationType.ChamberB => Golbal.CassetteSlotMin,       // ChamberB只有1个SLOT
                EnuLocationStationType.CoolingChamber => Golbal.CassetteSlotMin, // CoolingChamber只有1个SLOT
                EnuLocationStationType.CoolingTop => Golbal.CassetteSlotMin,     // CoolingTop只有1个SLOT
                EnuLocationStationType.CoolingBottom => Golbal.CassetteSlotMin,  // CoolingBottom只有1个SLOT
                _ => 1
            };
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        private async Task<bool> ShowConfirmDialog(string message, string title)
        {
            // 这里简化处理，实际应该使用对话框服务
            // 由于我们没有完整的对话框服务代码，这里直接返回true模拟用户确认
            // 在实际应用中，你应该使用如下代码：
            // return await _dialogService.ShowConfirmDialog(message, title);

            // 模拟对话框确认
            UILogService.AddLog($"提示: {message}");
            // 在实际应用中应该替换为真实的对话框确认
            return await Task.FromResult(true);
        }

        #endregion 方法

        #region 命令

        /// <summary>
        /// InterLock数据测试
        /// </summary>
        /// <returns></returns>
        [RelayCommand]
        private async Task OnDevTest()
        {
            try
            {
                //日志层次感服务类测试
                //await HierarchicalLoggingTest.RunAllTests();
                //await new HierarchicalLoggingExample().RunAllExamples();
                //await HierarchicalWaferOperationsExample.ComplexWaferProcessingWorkflowAsync();
                //return;

                #region InterLock数据测试

                /*UpdateRAxisZeroStatus();
                return;

                //InterLockAccessHelper 来优化 InterLock 调用
                InterLockAccessOptimizationExample.RunAllExamples();
                return;*/

                //await SS200InterLockMain_DirectStatusAccess_Example.RunAllDirectStatusAccessExamples();

                #region InterLock统一入口测试

                /// <summary>
                /// SS200InterLockMain - 单例模式的统一访问入口
                /// 提供对设备IO、报警信息、配置信息、状态信息的统一访问
                ///
                /// 使用示例：
                /// // IO调用
                /// bool robotDI1Value = _interlock.IOInterface.Robot.RDI1_PaddleSmoothSensor.Value;
                /// string robotDI1Content = _interlock.IOInterface.Robot.RDI1_PaddleSmoothSensor.Content;
                ///
                /// // 报警调用
                /// string alarmContent = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.Content;
                /// string alarmChsContent = _interlock.AlarmCode.Robot.RA1_SystemBusyReject.ChsContent;
                ///
                /// // 配置调用
                /// int positionValue = _interlock.SubsystemConfigure.PositionValue.RP1_TAxisPosition.Value;
                ///
                /// // 状态调用 - 直接访问完整状态实体对象
                /// var robotStatus = _interlock.SubsystemStatus.Robot.Status;
                /// </summary>

                // 缓存 InterLock 实例，减少重复的单例访问

                {
                    //IO接口获取传感器状态 - 缓存IO接口对象
                    var ioInterface = _interlock.IOInterface;
                    var valueRobot = ioInterface.Robot.RDI1_PaddleSensor1Left.Value;
                    var valueChamberA = ioInterface.ChamberA.PDI12_SlitDoorOpenSensor.Value;
                    var valueChamberB = ioInterface.ChamberB.PDI12_SlitDoorOpenSensor.Value;
                    var valueShuttle = ioInterface.Shuttle.SDI6_PresentSensorCassette1.Value;
                }
                {
                    //报警信息获取 - 缓存报警代码对象
                    var alarmCode = _interlock.AlarmCode;
                    var alarmRobot = alarmCode.Robot.RA1_SystemBusyReject.Content;
                    var alarmChamberA = alarmCode.ChamberA.PAC1_SystemAbnormalReject.Content;
                    var alarmChamberB = alarmCode.ChamberA.PAC1_SystemAbnormalReject.Content;
                    var alarmShuttle = alarmCode.Shuttle.SA7_ShuttleMoveTooFast.Content;
                }

                {
                    //配置参数 - 缓存配置对象
                    var configure = _interlock.SubsystemConfigure;
                    var location_Robot = configure.Robot.RP1_TAxisSmoothToCHA?.Value ?? 0;
                    var config_robot = configure.Robot.RPS1_RobotRotateSpeed?.Value ?? 0;

                    var config_ChamberA = configure.ChamberA.PPS1_SlitDoorMotionMinTime?.DoubleValue ?? 0;//Json配置参数有小数点，比如"value": 0.3，使用DoubleValue获取正确的小数值
                    var config_ChamberB = configure.ChamberB.PPS1_SlitDoorMotionMinTime?.DoubleValue ?? 0;//Json配置参数有小数点，比如"value": 0.3，使用DoubleValue获取正确的小数值
                    var config_ChamberB2 = configure.ChamberB.PPS2_SlitDoorMotionMaxTime?.Value ?? 0;//Json配置参数，比如"value":3，整数参数使用Value

                    var config_Shuttle = configure.Shuttle.SPS1_CassetteNestExtendRetractMinTime?.Value ?? 0;

                    var config_MainSys = configure.MainSystem.SSC1_Shuttle1WaferSize?.Value ?? 0;
                }

                {
                    //状态信息获取 - 缓存状态对象
                    var subsystemStatus = _interlock.SubsystemStatus;
                    var statusRobot = subsystemStatus.Robot.Status;
                    var statusChamberA = subsystemStatus.ChamberA.Status;
                    var statusChamberB = subsystemStatus.ChamberB.Status;
                    var statusShuttle = subsystemStatus.Shuttle.Status;

                    // 推荐使用直接访问方式：statusRobot.EnuRobotStatus
                }

                #endregion InterLock统一入口测试

                //测试T轴/R轴只需要CoolingChamber，而Z轴需要CoolingTop/CoolingBottom的问题
                //CoolingChamberLocationUsageExample.RunAllExamples();

                // HcGrowlExtensionsExamples hcGrowlExtensionsExamples = new HcGrowlExtensionsExamples();
                // await hcGrowlExtensionsExamples.RunAllExamples();

                //SS200InterLockMainUsageExample ss200InterLock = new SS200InterLockMainUsageExample();
                //await ss200InterLock.RunAllExamples();

                HcGrowlExtensions.DeviceInfo(EnuMcuDeviceType.Robot, "InterLock数据测试完成");

                /*
                var robotSettingValue = RobotConfigureSettings.GetSettingValue(EnuRobotConfigureSettingCodes.RPS1);
                HcGrowlExtensions.Info($"robotSettingValue={robotSettingValue}");

                var loadlockPressureOffset = ShuttleConfigureSettings.Instance.LoadlockPressureOffset;
                HcGrowlExtensions.Info($"loadlockPressureOffset={loadlockPressureOffset}");

                var alarmItem = RobotErrorCodesProvider.Instance.GetAlarmItemByCode(EnuRobotAlarmCodes.RA1);
                HcGrowlExtensions.Info($"alarmItem={alarmItem?.Code}，{alarmItem?.Content}");
                */

                // var alarms = RobotErrorCodesProvider.Instance.GetAllAlarmItems();
                // HcGrowlExtensions.Info($"当前所有报警信息：{string.Join(",", alarms.Select(t => $"{t.Code}：{t.Content}\r\n"))}");

                #endregion InterLock数据测试

                /*if (File.Exists(Path.Combine(Golbal.WorkRootPath, "CodeFirst.tag")))
                {
                    await RunCodeFirst();
                    return;
                }

                if (false)
                {
                    dialogHost.Show(nameof(WaferInfoDisplay), new DialogParameters("message=Hello World"), result =>
                     {
                         if (result.Result == ButtonResult.OK)
                         {
                             // 处理用户点击OK按钮的逻辑
                         }
                         else if (result.Result == ButtonResult.Cancel)
                         {
                             // 处理用户点击取消按钮的逻辑
                         }
                     });
                }*/

                //var dialog = new WaferInfoDisplay();
                //dialog.Show();

                //MessageBox.Show(Host.Mode.ToString());

                #region UI界面Wafer跑马灯状态测试

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.AutoFinished;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.AutoFinished;
                //    await Task.Delay(200);
                //}

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.ManuFinished;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.ManuFinished;
                //    await Task.Delay(200);
                //}

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Others;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Others;
                //    await Task.Delay(200);
                //}

                //for (int i = 0; i < Cassette.LeftWaferAction.Wafers.Count; i++)
                //{
                //    Cassette.LeftWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Have;
                //    Cassette.RightWaferAction.Wafers[i].WaferStatus = Zishan.SS200.Cmd.Enums.EnuWaferStatus.Have;
                //    await Task.Delay(200);
                //}

                #endregion UI界面Wafer跑马灯状态测试

                //Cooling.ProcessTimer = TimeSpan.FromSeconds(90);
                // Cooling.ProcessTimer=TimeSpan.Zero;

                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _strMsg = $"OnDevTest 开发测试失败,Message：{ex.Message}";
                AppLog.Error(_strMsg, ex);
                MessageBox.Show(_strMsg);
            }
        }

        /// <summary>
        /// 验证InterLock数据正确性
        /// </summary>
        /// <returns></returns>
        [RelayCommand]
        private async Task OnDevInterLockVerify()
        {
            /*// 从IOC容器获取验证器实例
            var validator = App.GetInstance<SS200ConfigurationValidator>();

            // 执行完整验证
            var result = validator.ValidateAll();

            // 检查验证结果
            if (result.IsValid)
            {
                HcGrowlExtensions.Success("配置验证通过");
            }
            else
            {
                //错误合并
                string errorMessage = string.Join("\n", result.Errors);
                HcGrowlExtensions.Error($"配置验证失败：{errorMessage}");
            }*/

            try
            {
                _logger.Info("开始执行启动时配置验证...");

                // 从IOC容器获取验证器实例
                var validator = App.GetInstance<SS200ConfigurationValidator>();

                // 执行验证
                var result = validator.ValidateOnStartup(blAlwaysShowResult: true);

                _logger.Info($"配置验证完成，结果: {(result.IsValid ? "通过" : "有警告")}");
            }
            catch (Exception ex)
            {
                _logger.Error($"启动时配置验证失败: {ex.Message}", ex);

                // 显示错误消息，但不阻止程序启动
                MessageBox.Show(
                    $"配置验证过程中发生错误，但程序将继续运行。\n错误信息: {ex.Message}",
                    "配置验证警告",
                    MessageBoxButton.OK,
                    MessageBoxImage.Warning);
            }

            await Task.CompletedTask;
        }

        #region T轴旋转测试

        /// <summary>
        /// 移动T轴到指定位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveTAxisToLocation(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析参数 (格式: "端口类型,位置类型")
                var parts = parameter.Split(',');
                if (parts.Length != 2)
                {
                    UILogService.AddLog("参数格式错误");
                    return;
                }

                // 解析端口类型
                EnuRobotEndType endType = parts[0] == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;
                CurrentActiveEndType = endType;

                // 解析位置类型
                EnuLocationStationType locationType;
                if (!Enum.TryParse(parts[1], out locationType))
                {
                    UILogService.AddLog($"无效的位置类型: {parts[1]}");
                    return;
                }

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 安全模式下检查R轴是否已回原点 - 使用智能状态获取
                if (IsSafetyModeEnabled && !GetCurrentRAxisZeroStatus())
                {
                    string message = $"安全限制：R轴必须先回原点，才能执行T轴旋转操作";
                    UILogService.AddLog($"⚠️ {message}");
                    HcGrowlExtensions.Warning(message);

                    // 提示用户是否自动执行R轴归零
                    var result = await ShowConfirmDialog($"是否自动执行{endType}端R轴归零操作？", "操作确认");
                    if (result)
                    {
                        // 用户确认执行R轴归零
                        UILogService.AddLog($"用户已确认，开始执行{endType}端R轴归零...");
                        await ZeroRAxis(endType.ToString());

                        // 检查归零是否成功 - 使用智能状态获取
                        if (!GetCurrentRAxisZeroStatus())
                        {
                            UILogService.AddLog("❌ R轴归零失败，无法执行T轴旋转操作");
                            return;
                        }
                    }
                    else
                    {
                        // 用户取消操作
                        UILogService.AddLog("❌ 用户取消了R轴归零操作，T轴旋转操作已取消");
                        return;
                    }
                }

                UILogService.AddLog($"开始移动{endType}端T轴到{locationType}位置...");
                EnsureCancellationTokenSourceValid();
                var moveResult = await _mcuCmdService.MoveTAxisToLocationAsync(endType, locationType, _cancellationTokenSource.Token);

                if (moveResult.Success)
                {
                    // 更新T轴当前位置
                    CurrentTAxisLocation = locationType;
                    UILogService.AddLog($"✅ {endType}端T轴移动到{locationType}位置成功");
                    //HcGrowlExtensions.Success($"{endType}端T轴移动到{locationType}位置成功");
                }
                else
                {
                    UILogService.AddLog($"❌ {endType}端T轴移动到{locationType}位置失败: {moveResult.Message}");
                    HcGrowlExtensions.Error($"{endType}端T轴移动到{locationType}位置失败: {moveResult.Message}");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ T轴移动异常: {ex.Message}");
                HcGrowlExtensions.Error($"T轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// T轴归零命令【不区分Nose端和Smooth端】
        /// </summary>
        [RelayCommand]
        private async Task ZeroTAxis(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                UILogService.AddLog($"开始{endType}端T轴归零...");
                var result = await _mcuCmdService.ZeroTAxisAsync(_cancellationTokenSource.Token);//在执行T轴归零过程中有R轴归零，IsRAxisAtZero相应的UI界面上的轴状态：R轴已归零

                // T轴归零完成后，动态更新R轴状态 - 因为T轴归零过程中包含R轴归零操作
                UpdateRAxisZeroStatus();

                if (result.Success)
                {
                    UILogService.AddLog($"✅ {endType}端T轴归零成功");

                    // 记录R轴状态更新信息
                    string rAxisStatusMsg = IsRAxisAtZero ? "R轴已归零" : "R轴未归零";
                    UILogService.AddLog($"📍 T轴归零过程中R轴状态: {rAxisStatusMsg}");
                }
                else
                {
                    UILogService.AddLog($"❌ {endType}端T轴归零失败: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ T轴归零异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        #endregion T轴旋转测试

        #region R轴伸缩测试

        /// <summary>
        /// 移动R轴到指定位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveRAxisToLocation(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析参数 (格式: "端口类型,位置类型")
                var parts = parameter.Split(',');
                if (parts.Length != 2)
                {
                    UILogService.AddLog("参数格式错误");
                    return;
                }

                // 解析端口类型
                EnuRobotEndType endType = parts[0] == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;
                CurrentActiveEndType = endType;

                // 解析位置类型
                EnuLocationStationType locationType;
                if (!Enum.TryParse(parts[1], out locationType))
                {
                    UILogService.AddLog($"无效的位置类型: {parts[1]}");
                    return;
                }

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 安全模式下检查T轴是否已经转到对应方向
                if (IsSafetyModeEnabled && CurrentTAxisLocation != locationType)
                {
                    string message = $"安全限制：T轴必须先转到{locationType}方向，才能执行R轴伸缩操作";
                    UILogService.AddLog($"⚠️ {message}");
                    HcGrowlExtensions.Warning(message);

                    // 提示用户是否自动执行T轴旋转
                    var result = await ShowConfirmDialog($"是否自动执行{endType}端T轴转到{locationType}位置？", "操作确认");
                    if (result)
                    {
                        // 用户确认执行T轴旋转
                        UILogService.AddLog($"用户已确认，开始执行{endType}端T轴转到{locationType}位置...");

                        // 执行T轴旋转
                        string tAxisParam = $"{endType},{locationType}";
                        await MoveTAxisToLocation(tAxisParam);

                        // 检查T轴旋转是否成功
                        if (CurrentTAxisLocation != locationType)
                        {
                            UILogService.AddLog($"❌ T轴转到{locationType}方向失败，无法执行R轴伸缩操作");
                            return;
                        }
                    }
                    else
                    {
                        // 用户取消操作
                        UILogService.AddLog("❌ 用户取消了T轴旋转操作，R轴伸缩操作已取消");
                        return;
                    }
                }

                // 当R轴执行移动操作时，标记R轴不在原点
                IsRAxisAtZero = false;

                UILogService.AddLog($"开始移动{endType}端R轴到{locationType}位置...");
                var moveResult = await _mcuCmdService.MoveRAxisToLocationAsync(endType, locationType, _cancellationTokenSource.Token);

                if (moveResult.Success)
                {
                    UILogService.AddLog($"✅ {endType}端R轴移动到{locationType}位置成功");
                    //HcGrowlExtensions.Success($"{endType}端R轴移动到{locationType}位置成功");
                }
                else
                {
                    UILogService.AddLog($"❌ {endType}端R轴移动到{locationType}位置失败: {moveResult.Message}");
                    HcGrowlExtensions.Error($"{endType}端R轴移动到{locationType}位置失败: {moveResult.Message}");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ R轴移动异常: {ex.Message}");
                HcGrowlExtensions.Error($"R轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// R轴归零命令【不区分Nose端和Smooth端】
        /// </summary>
        [RelayCommand]
        private async Task ZeroRAxis(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;
                CurrentActiveEndType = endType;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                UILogService.AddLog($"开始{endType}端R轴归零...");
                var result = await _mcuCmdService.ZeroRAxisAsync(_cancellationTokenSource.Token);

                if (result.Success)
                {
                    // 更新R轴状态为已归零
                    IsRAxisAtZero = true;
                    // 同时更新实时状态表（如果可用）
                    UpdateRAxisZeroStatus();
                    UILogService.AddLog($"✅ {endType}端R轴归零成功");
                    //HcGrowlExtensions.Success($"{endType}端R轴归零成功");
                }
                else
                {
                    // 更新R轴状态为未归零
                    IsRAxisAtZero = false;
                    UILogService.AddLog($"❌ {endType}端R轴归零失败: {result.Message}");
                    HcGrowlExtensions.Error($"{endType}端R轴归零失败: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                IsRAxisAtZero = false;
                UILogService.AddLog($"❌ R轴归零异常: {ex.Message}");
                HcGrowlExtensions.Error($"R轴归零异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        #endregion R轴伸缩测试

        #region Z轴升降测试

        /// <summary>
        /// 移动Z轴到Get位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveZAxisToGetPosition(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 获取选中的位置类型和Slot号
                if (SelectedLocationType == null)
                {
                    UILogService.AddLog("请先选择位置类型");
                    return;
                }

                int slot = 1;
                if (!int.TryParse(SlotNumber, out slot))
                {
                    UILogService.AddLog("无效的Slot号");
                    return;
                }

                UILogService.AddLog($"开始移动{endType}端Z轴到{SelectedLocationType.StationType}(Slot:{slot})的Get位置...");
                var moveResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(endType, SelectedLocationType.StationType, slot, _cancellationTokenSource.Token);

                if (!moveResult.Success)
                {
                    UILogService.AddLog($"❌ {endType}端Z轴移动到{SelectedLocationType.StationType}(Slot:{slot})的Get位置失败: {moveResult.Message}");
                    //UILogService.AddLog($"✅ {endType}端Z轴移动到{SelectedLocationType.StationType}(Slot:{slot})的Get位置成功");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ Z轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// 移动Z轴到Put位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveZAxisToPutPosition(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 获取选中的位置类型和Slot号
                if (SelectedLocationType == null)
                {
                    UILogService.AddLog("请先选择位置类型");
                    return;
                }

                int slot = 1;
                if (!int.TryParse(SlotNumber, out slot))
                {
                    UILogService.AddLog("无效的Slot号");
                    return;
                }

                UILogService.AddLog($"开始移动{endType}端Z轴到{SelectedLocationType.StationType}(Slot:{slot})的Put位置...");
                var result = await _mcuCmdService.MoveZAxisToPutPositionAsync(endType, SelectedLocationType.StationType, slot, _cancellationTokenSource.Token);

                if (!result.Success)
                {
                    UILogService.AddLog($"❌ {endType}端Z轴移动到{SelectedLocationType.StationType}(Slot:{slot})的Put位置失败: {result.Message}");
                    //UILogService.AddLog($"✅ {endType}端Z轴移动到{SelectedLocationType.StationType}(Slot:{slot})的Put位置成功");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ Z轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// 移动Z轴到指定位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveZAxisToPosition(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 解析位置值
                int position;
                if (!int.TryParse(ZAxisPosition, out position))
                {
                    UILogService.AddLog("无效的Z轴位置值");
                    return;
                }

                UILogService.AddLog($"开始移动{endType}端Z轴到{position}步数位置...");
                var result = await _mcuCmdService.MoveZAxisToPositionAsync(position, _cancellationTokenSource.Token);

                if (!result.Success)
                {
                    UILogService.AddLog($"❌ {endType}端Z轴移动到{position}步数位置失败: {result.Message}");
                    //UILogService.AddLog($"✅ {endType}端Z轴移动到{position}步数位置成功");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ Z轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// Z轴归零命令
        /// </summary>
        [RelayCommand]
        private async Task ZeroZAxis(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                UILogService.AddLog($"开始{endType}端Z轴归零...");
                var result = await _mcuCmdService.ZeroZAxisAsync(_cancellationTokenSource.Token);

                if (!result.Success)
                {
                    UILogService.AddLog($"❌ {endType}端Z轴归零失败: {result.Message}");
                    //UILogService.AddLog($"✅ {endType}端Z轴归零成功");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ Z轴归零异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// 移动T轴到指定位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveTAxisToPosition(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 安全模式下检查R轴是否已回原点 - 使用智能状态获取
                if (IsSafetyModeEnabled && !GetCurrentRAxisZeroStatus())
                {
                    string message = $"安全限制：R轴必须先回原点，才能执行T轴旋转操作";
                    UILogService.AddLog($"⚠️ {message}");
                    HcGrowlExtensions.Warning(message);

                    // 提示用户是否自动执行R轴归零
                    var dialogResult = await ShowConfirmDialog($"是否自动执行{endType}端R轴归零操作？", "操作确认");
                    if (dialogResult)
                    {
                        // 用户确认执行R轴归零
                        UILogService.AddLog($"用户已确认，开始执行{endType}端R轴归零...");
                        await ZeroRAxis(endType.ToString());

                        // 检查归零是否成功 - 使用智能状态获取
                        if (!GetCurrentRAxisZeroStatus())
                        {
                            UILogService.AddLog("❌ R轴归零失败，无法执行T轴旋转操作");
                            return;
                        }
                    }
                    else
                    {
                        // 用户取消操作
                        UILogService.AddLog("❌ 用户取消了R轴归零操作，T轴旋转操作已取消");
                        return;
                    }
                }

                // 解析位置值
                int position;
                if (!int.TryParse(TAxisPosition, out position))
                {
                    UILogService.AddLog("无效的T轴位置值");
                    return;
                }

                UILogService.AddLog($"开始移动{endType}端T轴到{position}步数位置...");
                var result = await _mcuCmdService.MoveTAxisToPositionAsync(position, _cancellationTokenSource.Token);

                if (!result.Success)
                {
                    UILogService.AddLog($"❌ {endType}端T轴移动到{position}步数位置失败: {result.Message}");
                    //UILogService.AddLog($"✅ {endType}端T轴移动到{position}步数位置成功");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ T轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        /// <summary>
        /// 移动R轴到指定位置命令
        /// </summary>
        [RelayCommand]
        private async Task MoveRAxisToPosition(string parameter)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                IsExecutingCommand = true;

                // 解析端口类型
                EnuRobotEndType endType = parameter == "Nose" ? EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 检查Robot连接状态
                if (!_mcuCmdService.Robot.IsConnected)
                {
                    UILogService.AddLog("Robot未连接，请先连接设备");
                    return;
                }

                // 安全模式下检查T轴是否已经转到对应方向
                if (IsSafetyModeEnabled)
                {
                    string message = $"安全提示：请确保T轴已转到合适方向，再执行R轴伸缩操作";
                    UILogService.AddLog($"⚠️ {message}");
                    HcGrowlExtensions.Warning(message);

                    // 提示用户确认继续
                    var dialogResult = await ShowConfirmDialog($"确认T轴已在合适位置，继续执行{endType}端R轴伸缩操作？", "操作确认");
                    if (!dialogResult)
                    {
                        // 用户取消操作
                        UILogService.AddLog("❌ 用户取消了R轴伸缩操作");
                        return;
                    }
                }

                // 解析位置值
                int position;
                if (!int.TryParse(RAxisPosition, out position))
                {
                    UILogService.AddLog("无效的R轴位置值");
                    return;
                }

                UILogService.AddLog($"开始移动{endType}端R轴到{position}步数位置...");
                var result = await _mcuCmdService.MoveRAxisToPositionAsync(position, true, true, 10000, _cancellationTokenSource.Token);

                if (result.Success)
                {
                    // 如果是归零位置，更新归零状态
                    int zeroPosition = RobotPositionParameters.GetRAxisZeroPosition();
                    if (position == zeroPosition)
                    {
                        IsRAxisAtZero = true;
                        // 同时更新实时状态表（如果可用）
                        UpdateRAxisZeroStatus();
                    }
                    else
                    {
                        // 不在零位时，标记为非零位
                        IsRAxisAtZero = false;
                    }

                    UILogService.AddLog($"✅ {endType}端R轴移动到{position}步数位置成功");
                }
                else
                {
                    UILogService.AddLog($"❌ {endType}端R轴移动到{position}步数位置失败: {result.Message}");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"❌ R轴移动异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
            }
        }

        #endregion Z轴升降测试

        /// <summary>
        /// 根据Combox选择的端口类型执行机械臂命令
        /// </summary>
        [RelayCommand]
        private async Task ExecuteSelectedRobotCmd()
        {
            if (IsExecutingCommand)
                return;

            try
            {
                // 记录当前Robot连接状态
                UILogService.AddLog($"执行Robot命令，Robot连接状态: {_mcuCmdService.Robot?.IsConnected}，命令: {SelectedRobotCommand.GetDescription()}");

                // 检查设备连接状态
                if (_mcuCmdService == null)
                {
                    _strMsg = "命令服务为空，请检查依赖注入";
                    HcGrowlExtensions.Warning(_strMsg);
                    UILogService.AddLog(_strMsg);
                    _logger.Error(_strMsg);
                    return;
                }

                if (!_mcuCmdService.Robot.IsConnected)
                {
                    _strMsg = "请先连接Robot设备";
                    HcGrowlExtensions.Warning(_strMsg);
                    UILogService.AddLog(_strMsg);
                    _logger.Error($"Robot未连接，无法执行命令");
                    return;
                }

                // 设置开始执行命令状态
                IsExecutingCommand = true;

                // 执行选中的Robot命令
                UILogService.AddLog($"开始执行Robot命令: {SelectedRobotCommand.GetDescription()}");
                (bool Success, string Message) CommandResult;
                switch (SelectedRobotCommand)
                {
                    case EnuRobotCmd.AR1:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR2:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR3:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR4:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR5:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR6:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR7:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR8:
                        CommandResult = await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR9:
                        CommandResult = await _mcuCmdService.ZeroTAxisAsync(_cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR10:
                        // 解析T轴位置值
                        int tAxisPosition;
                        if (!int.TryParse(TAxisPosition, out tAxisPosition))
                        {
                            UILogService.AddLog("无效的T轴位置值");
                            return;
                        }
                        CommandResult = await _mcuCmdService.MoveTAxisToPositionAsync(tAxisPosition, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR11:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR12:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR13:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR14:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR15:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR16:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR17:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR18:
                        CommandResult = await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR19:
                        CommandResult = await _mcuCmdService.ZeroRAxisAsync(_cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR20:
                        // 解析R轴位置值
                        int rAxisPosition;
                        if (!int.TryParse(RAxisPosition, out rAxisPosition))
                        {
                            UILogService.AddLog("无效的R轴位置值");
                            return;
                        }
                        CommandResult = await _mcuCmdService.MoveRAxisToPositionAsync(rAxisPosition, true, true, 10000, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR21:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA);
                        break;

                    case EnuRobotCmd.AR22:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB);
                        break;

                    case EnuRobotCmd.AR23:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingTop);
                        break;

                    case EnuRobotCmd.AR24:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingBottom);
                        break;

                    case EnuRobotCmd.AR25:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA);
                        break;

                    case EnuRobotCmd.AR26:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB);
                        break;

                    case EnuRobotCmd.AR27:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingTop);
                        break;

                    case EnuRobotCmd.AR28:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingBottom);
                        break;

                    case EnuRobotCmd.AR29:
                        CommandResult = await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingTop, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR30:
                        CommandResult = await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingBottom, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR31:
                        CommandResult = await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingTop, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR32:
                        CommandResult = await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingBottom, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR33:
                        CommandResult = await _mcuCmdService.ZeroZAxisAsync(_cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR34:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetSlotPositionAsync(EnuRobotEndType.Smooth, SlotNumber);
                        break;

                    case EnuRobotCmd.AR35:
                        //CommandResult = await _mcuCmdService.MoveZAxisToGetSlotPositionAsync(EnuRobotEndType.Nose, SlotNumber);
                        break;

                    case EnuRobotCmd.AR36:
                        //CommandResult = await _mcuCmdService.MoveZAxisToPutSlotPositionAsync(EnuRobotEndType.Smooth, SlotNumber);
                        break;

                    case EnuRobotCmd.AR37:
                        //CommandResult = await _mcuCmdService.MoveZAxisToPutSlotPositionAsync(EnuRobotEndType.Nose, SlotNumber);
                        break;

                    case EnuRobotCmd.AR38:
                        //CommandResult = await _mcuCmdService.MoveZAxisToPinSearchPositionAsync();
                        break;

                    case EnuRobotCmd.AR39:
                        // 解析Z轴位置值
                        int zAxisPosition;
                        if (!int.TryParse(ZAxisPosition, out zAxisPosition))
                        {
                            UILogService.AddLog("无效的Z轴位置值");
                            return;
                        }
                        CommandResult = await _mcuCmdService.MoveZAxisToPositionAsync(zAxisPosition, _cancellationTokenSource.Token);
                        break;

                    case EnuRobotCmd.AR40:
                    case EnuRobotCmd.AR41:
                        // 位置增量调整，暂不实现
                        CommandResult = (false, "位置增量调整功能尚未实现");
                        break;

                    case EnuRobotCmd.AR42:
                        //CommandResult = await _mcuCmdService.PinSearchAsync();
                        break;

                    case EnuRobotCmd.AR43:
                        //CommandResult = await _mcuCmdService.GetWaferFromCassetteAsync(SlotNumber);
                        break;

                    case EnuRobotCmd.AR44:
                        break;

                    case EnuRobotCmd.AR45:
                        break;

                    case EnuRobotCmd.AR46:
                        break;

                    case EnuRobotCmd.AR47:
                        break;

                    case EnuRobotCmd.AR48:
                        break;

                    case EnuRobotCmd.AR49:
                        break;

                    case EnuRobotCmd.AR50:
                        break;

                    case EnuRobotCmd.AR51:
                        break;

                    case EnuRobotCmd.AR52:
                        break;

                    case EnuRobotCmd.AR53:
                        break;

                    case EnuRobotCmd.AR54:
                        break;

                    case EnuRobotCmd.AR55:
                        break;

                    case EnuRobotCmd.AR56:
                        break;

                    case EnuRobotCmd.AR57:
                        break;

                    case EnuRobotCmd.AR58:
                        break;

                    case EnuRobotCmd.AR59:
                        break;

                    case EnuRobotCmd.AR60:
                        break;

                    case EnuRobotCmd.AR61:
                        break;

                    case EnuRobotCmd.AR62:
                        break;

                    case EnuRobotCmd.AR63:
                        break;

                    case EnuRobotCmd.AR64:
                        break;

                    case EnuRobotCmd.AR65:
                        break;

                    case EnuRobotCmd.AR66:
                        break;

                    case EnuRobotCmd.AR67:
                        break;

                    case EnuRobotCmd.AR68:
                        break;

                    case EnuRobotCmd.AR69:
                        break;

                    case EnuRobotCmd.AR70:
                        break;

                    case EnuRobotCmd.AR71:
                        break;

                    case EnuRobotCmd.AR72:
                        break;

                    case EnuRobotCmd.AR73:
                        break;

                    case EnuRobotCmd.AR74:
                        break;

                    case EnuRobotCmd.AR75:
                        break;

                    case EnuRobotCmd.AR76:
                        break;

                    case EnuRobotCmd.AR77:
                        break;

                    case EnuRobotCmd.AR78:
                        break;

                    case EnuRobotCmd.AR79:
                        break;

                    case EnuRobotCmd.AR80:
                        break;

                    case EnuRobotCmd.AR81:
                        break;

                    case EnuRobotCmd.AR82:
                        break;

                    case EnuRobotCmd.AR83:
                        break;

                    case EnuRobotCmd.AR84:
                        break;

                    case EnuRobotCmd.AR85:
                        break;

                    case EnuRobotCmd.AR86:
                        break;

                    case EnuRobotCmd.AR87:
                        break;

                    case EnuRobotCmd.AR88:
                        break;

                    case EnuRobotCmd.AR89:
                        break;

                    case EnuRobotCmd.AR90:
                        break;

                    case EnuRobotCmd.AR91:
                        break;

                    case EnuRobotCmd.AR92:
                        break;

                    case EnuRobotCmd.AR93:
                        break;

                    case EnuRobotCmd.AR94:
                        break;

                    case EnuRobotCmd.AR95:
                        break;

                    case EnuRobotCmd.AR96:
                        break;

                    case EnuRobotCmd.AR97:
                        break;

                    case EnuRobotCmd.AR98:
                        break;
                }

                //var result = await _mcuCmdService.ExecuteRobotCommandAsync(SelectedRobotCommand);

                // 记录和通知结果
                var (response, runInfo, returnInfo) = ("mock ok", "runInfo", "returnInfo");
                bool isSuccess = !response.StartsWith("Error:") && !response.StartsWith("Failed:");

                if (isSuccess)
                {
                    _strMsg = $"执行Robot命令 {SelectedRobotCommand.GetDescription()} 成功: {response}";
                    HcGrowlExtensions.Success(_strMsg);
                    UILogService.AddLog($"✅ {_strMsg}");
                    CmdResult = $"成功: {response}，运行信息: {runInfo}，返回信息: {returnInfo}";
                }
                else
                {
                    _strMsg = $"执行Robot命令 {SelectedRobotCommand.GetDescription()} 失败: {response}";
                    HcGrowlExtensions.Error(_strMsg);
                    UILogService.AddLog($"❌ {_strMsg}");
                    CmdResult = $"失败: {response}，运行信息: {runInfo}，返回信息: {returnInfo}";
                }
            }
            catch (Exception ex)
            {
                _strMsg = $"执行Robot命令异常: {ex.Message}";
                HcGrowlExtensions.Error(_strMsg);
                UILogService.AddLog($"❌ {_strMsg}");
                _logger.Error($"执行Robot命令异常", ex);
            }
            finally
            {
                IsExecutingCommand = false;
            }
        }

        #region 整个源、目标、机械臂 晶圆搬运测试

        /*
        这个方法执行完，通过UILogService记录的日志在UI上展示的很多，不断地一层一层调用，调用的很多，日志没有层次感，查看不友好，怎么有层次感,例如通过一层层缩进：
        1. 执行搬运，Robot连接状态: True，服务实例: McuCmdService
        2. 搬运Wafer参数 - From: ChamberA(SLOT:1), To: ChamberB(SLOT:2) 机械臂端口: Nose
        3.    取Wafer: 从ChamberA(SLOT:1)获取Wafer
        4.           开始取Wafer...
        5.           结束取Wafer...
        6.    放Wafer: 从ChamberA(SLOT:1)获取Wafer
        7. 搬运Wafer成功: Wafer已成功从ChamberA(SLOT:1)搬运到ChamberB(SLOT:2)
        */

        /// <summary>
        /// 整个源、目标、机械臂 晶圆搬运测试 - 优化版本（使用通用框架）
        /// </summary>
        [RelayCommand]
        private async Task TrasferWafer()
        {
            // 设置CommandResult为空
            CommandResult = string.Empty;

            await ExecuteTestMethodAsync("晶圆搬运", "搬运", ExecuteTrasferWaferLoopLogicAsync);
        }

        /// <summary>
        /// 晶圆搬运循环逻辑执行器（适配通用框架）
        /// </summary>
        private async Task<bool> ExecuteTrasferWaferLoopLogicAsync(int currentLoop, bool isInfiniteLoop, int remainingCount, CancellationToken cancellationToken)
        {
            // 🔥 循环级别晶圆搬运性能监控
            var loopStopwatch = new StopwatchHelper($"晶圆搬运循环-第{currentLoop}次-{SelectedFromChamber?.ChamberName}→{SelectedToChamber?.ChamberName}");
            loopStopwatch.Start();

            try
            {
                // 获取搬运参数
                EnuLocationStationType fromStationType = SelectedFromChamber?.StationType ?? EnuLocationStationType.Cassette;
                EnuLocationStationType toStationType = SelectedToChamber?.StationType ?? EnuLocationStationType.ChamberA;
                EnuRobotEndType endType = SelectedByArmFetchSide?.Contains("Nose") == true ?
                    EnuRobotEndType.Nose : EnuRobotEndType.Smooth;

                // 显示搬运参数
                await UpdateUIAsync(() =>
                {
                    UILogService.AddLog($"搬运Wafer参数 - From: {SelectedFromChamber?.ChamberName}(SLOT:{SelectedFromSlot}), To: {SelectedToChamber?.ChamberName}(SLOT:{SelectedToSlot}) 机械臂端口: {endType}");
                });

                // 检查取消状态
                if (cancellationToken.IsCancellationRequested)
                {
                    loopStopwatch.Stop(warnThresholdMs: 40000);
                    return false;
                }

                // 执行晶圆搬运操作
                var result = await ExecuteSingleTrasferWaferAsync(endType, fromStationType, toStationType,
                    SelectedFromSlot, SelectedToSlot, cancellationToken);

                // 处理搬运结果
                var handleResult = await HandleTrasferWaferResultAsync(result, currentLoop, isInfiniteLoop, remainingCount);

                // 🔥 循环级别性能监控结束 - 成功
                loopStopwatch.Stop(warnThresholdMs: 40000);
                return handleResult;
            }
            catch (OperationCanceledException)
            {
                // 🔥 循环级别性能监控结束 - 取消
                loopStopwatch.Stop(warnThresholdMs: 40000);
                await UpdateUIAsync(() => UILogService.AddWarningLog("晶圆搬运操作已被取消"));
                return false;
            }
            catch (Exception ex)
            {
                // 🔥 循环级别性能监控结束 - 异常
                loopStopwatch.Stop(warnThresholdMs: 40000);
                await UpdateUIAsync(() =>
                {
                    UILogService.AddErrorLog($"晶圆搬运操作异常: {ex.Message}");
                    _logger.Error("晶圆搬运操作异常", ex);
                });
                return false;
            }
        }

        /// <summary>
        ///  优化：执行单次晶圆搬运操作
        /// </summary>
        private async Task<(bool Success, string Message)> ExecuteSingleTrasferWaferAsync(
            EnuRobotEndType endType,
            EnuLocationStationType fromStationType,
            EnuLocationStationType toStationType,
            int fromSlot,
            int toSlot,
            CancellationToken cancellationToken)
        {
            // 🔥 单次晶圆搬运性能监控
            var singleTransferStopwatch = new StopwatchHelper($"单次晶圆搬运-{endType}端-{fromStationType}(Slot:{fromSlot})→{toStationType}(Slot:{toSlot})");
            singleTransferStopwatch.Start();

            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                var result = await _mcuCmdService.TrasferWaferAsync(endType, fromStationType, toStationType, fromSlot, toSlot, IsTRZAxisReturnZeroed, _cancellationTokenSource.Token);

                // 🔥 性能监控结束 - 成功
                singleTransferStopwatch.Stop(warnThresholdMs: 35000);
                return result;
            }
            catch (OperationCanceledException)
            {
                // 🔥 性能监控结束 - 取消
                singleTransferStopwatch.Stop(warnThresholdMs: 35000);
                return (false, "操作已取消");
            }
            catch (Exception ex)
            {
                // 🔥 性能监控结束 - 异常
                singleTransferStopwatch.Stop(warnThresholdMs: 35000);
                return (false, $"晶圆搬运执行异常: {ex.Message}");
            }
        }

        /// <summary>
        ///  优化：处理晶圆搬运结果
        /// </summary>
        private async Task<bool> HandleTrasferWaferResultAsync(
            (bool Success, string Message) result,
            int currentLoop,
            bool isInfiniteLoop,
            int remainingCount)
        {
            if (result.Success)
            {
                // 成功处理
                await UpdateUIAsync(() =>
                {
                    string successMsg = $"搬运Wafer成功: {result.Message}";
                    CommandResult = successMsg;
                    UILogService.AddLog($"✅ {successMsg}");
                    HcGrowlExtensions.Success($"晶圆搬运成功: {result.Message}", waitTime: 3);
                });
                return true;
            }
            else
            {
                // 失败处理
                await UpdateUIAsync(() =>
                {
                    string errorMsg = $"搬运Wafer失败: {result.Message}";
                    CommandResult = errorMsg;
                    UILogService.AddLog($"❌ {errorMsg}");
                    HcGrowlExtensions.Error($"晶圆搬运失败: {result.Message}", waitTime: 3);
                });

                // 如果还有剩余循环，询问是否继续
                if (remainingCount > 1 || isInfiniteLoop)
                {
                    bool shouldContinue = await ShowContinueConfirmationAsync(currentLoop, result.Message);
                    return shouldContinue;
                }

                return false;
            }
        }

        /// <summary>
        ///  优化：显示继续确认对话框（异步版本）
        /// </summary>
        private async Task<bool> ShowContinueConfirmationAsync(int currentLoop, string errorMessage)
        {
            bool result = false;
            await UpdateUIAsync(() =>
            {
                var continueResult = MessageBox.Show(
                    $"第{currentLoop}次搬运失败: {errorMessage}\n\n是否继续执行剩余的循环？",
                    "搬运失败确认",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Warning,
                    MessageBoxResult.No);

                result = continueResult == MessageBoxResult.Yes;

                if (!result)
                {
                    UILogService.AddWarningLog("用户选择停止循环执行");
                }
            });

            return result;
        }

        #endregion 整个源、目标、机械臂 晶圆搬运测试

        #region PinSearch 测试

        /// <summary>
        /// PinSearch 测试 - 优化版本（使用通用框架）
        /// </summary>
        [RelayCommand]
        private async Task OnPinSearchTest()
        {
            await ExecuteTestMethodAsync("PinSearch", "PinSearch", ExecutePinSearchLoopLogicAsync);
        }

        /// <summary>
        /// PinSearch循环逻辑执行器（适配通用框架）
        /// </summary>
        private async Task<bool> ExecutePinSearchLoopLogicAsync(int currentLoop, bool isInfiniteLoop, int remainingCount, CancellationToken cancellationToken)
        {
            try
            {
                // 清零PinSearch数据
                await ClearPinSearchDataAsync();

                // 检查取消状态
                if (cancellationToken.IsCancellationRequested)
                    return false;

                // 执行PinSearch操作
                var (smoothSuccess, noseSuccess) = await ExecutePinSearchOperationsAsync(cancellationToken);

                return smoothSuccess && noseSuccess;
            }
            catch (OperationCanceledException)
            {
                await UpdateUIAsync(() => UILogService.AddWarningLog("PinSearch操作已被取消"));
                return false;
            }
            catch (Exception ex)
            {
                await UpdateUIAsync(() =>
                {
                    UILogService.AddErrorLog($"PinSearch操作异常: {ex.Message}");
                    _logger.Error("PinSearch操作异常", ex);
                });
                return false;
            }
        }

        /// <summary>
        ///  优化：清零PinSearch数据的异步方法
        /// </summary>
        private async Task ClearPinSearchDataAsync()
        {
            await UpdateUIAsync(() => UILogService.AddInfoLog("清零PinSearch结果数据..."));

            // 获取当前机器人状态
            var robotStatus = _interlock.SubsystemStatus.Robot.Status;
            robotStatus.PinSearchStatus = false;
            robotStatus.EnuPinSearchDataEffective = EnuPinSearchStatus.None;

            // 清零服务中的基准值
            _mcuCmdService.SmoothBasePinSearchValue = 0;
            _mcuCmdService.NoseBasePinSearchValue = 0;

            // 清零机器人状态中的Shuttle1 PinSearch值
            robotStatus.Shuttle1PinSearchSmoothP1 = 0;
            robotStatus.Shuttle1PinSearchSmoothP2 = 0;
            robotStatus.Shuttle1PinSearchNoseP3 = 0;
            robotStatus.Shuttle1PinSearchNoseP4 = 0;

            // 清零机器人状态中的Shuttle2 PinSearch值
            robotStatus.Shuttle2PinSearchSmoothP1 = 0;
            robotStatus.Shuttle2PinSearchSmoothP2 = 0;
            robotStatus.Shuttle2PinSearchNoseP3 = 0;
            robotStatus.Shuttle2PinSearchNoseP4 = 0;

            await UpdateUIAsync(() => UILogService.AddSuccessLog("PinSearch结果数据已清零"));
        }

        /// <summary>
        ///  优化：执行PinSearch操作的异步方法
        /// </summary>
        private async Task<(bool smoothSuccess, bool noseSuccess)> ExecutePinSearchOperationsAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 执行 Smooth 端 PinSearch
                var smoothResult = await ExecuteSinglePinSearchAsync(EnuRobotEndType.Smooth, cancellationToken);
                if (!smoothResult.Success)
                {
                    await UpdateUIAsync(() =>
                    {
                        UILogService.AddErrorLog($"Smooth 端 PinSearch 测试失败: {smoothResult.Message}");
                        HcGrowlExtensions.Error($"Smooth 端 PinSearch 测试失败: {smoothResult.Message}");
                    });
                    return (false, false);
                }

                // 立即更新 Smooth 端结果到UI
                await UpdateSinglePinSearchResultAsync(smoothResult, EnuRobotEndType.Smooth);

                // 短暂延迟
                await Task.Delay(100, cancellationToken);

                // 执行 Nose 端 PinSearch
                var noseResult = await ExecuteSinglePinSearchAsync(EnuRobotEndType.Nose, cancellationToken);
                if (!noseResult.Success)
                {
                    await UpdateUIAsync(() =>
                    {
                        UILogService.AddErrorLog($"Nose 端 PinSearch 测试失败: {noseResult.Message}");
                        HcGrowlExtensions.Error($"Nose 端 PinSearch 测试失败: {noseResult.Message}");
                    });
                    return (true, false);
                }

                // 立即更新 Nose 端结果到UI
                await UpdateSinglePinSearchResultAsync(noseResult, EnuRobotEndType.Nose);

                return (true, true);
            }
            catch (OperationCanceledException)
            {
                await UpdateUIAsync(() => UILogService.AddWarningLog("PinSearch操作已被取消"));
                return (false, false);
            }
            catch (Exception ex)
            {
                await UpdateUIAsync(() =>
                {
                    UILogService.AddErrorLog($"PinSearch操作异常: {ex.Message}");
                    _logger.Error("PinSearch操作异常", ex);
                });
                return (false, false);
            }
        }

        /// <summary>
        ///  优化：执行单次PinSearch操作
        /// </summary>
        private async Task<(bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value)>
            ExecuteSinglePinSearchAsync(EnuRobotEndType endType, CancellationToken cancellationToken)
        {
            try
            {
                cancellationToken.ThrowIfCancellationRequested();
                return await _mcuCmdService.PinSearchAsync(endType, IsTRZAxisReturnZeroed, cancellationToken);
            }
            catch (OperationCanceledException)
            {
                return (false, "操作已取消", 0, 0);
            }
            catch (Exception ex)
            {
                return (false, $"PinSearch执行异常: {ex.Message}", 0, 0);
            }
        }

        /// <summary>
        /// 更新单个PinSearch结果到UI
        /// </summary>
        private async Task UpdateSinglePinSearchResultAsync(
            (bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value) result,
            EnuRobotEndType endType)
        {
            await UpdateUIAsync(() =>
            {
                var robotStatus = _interlock.SubsystemStatus.Robot.Status;

                if (endType == EnuRobotEndType.Smooth)
                {
                    robotStatus.Shuttle1PinSearchSmoothP1 = result.PinSearchP1Value;
                    robotStatus.Shuttle1PinSearchSmoothP2 = result.PinSearchP2Value;
                    string smoothMsg = $"执行 Smooth 端 PinSearch 测试成功: {result.Message}，P1Value={result.PinSearchP1Value}，P2Value={result.PinSearchP2Value}，平均值={_mcuCmdService.SmoothBasePinSearchValue}";
                    UILogService.AddSuccessLog(smoothMsg);
                    HcGrowlExtensions.Success(smoothMsg, waitTime: 5);
                }
                else if (endType == EnuRobotEndType.Nose)
                {
                    robotStatus.Shuttle1PinSearchNoseP3 = result.PinSearchP1Value;
                    robotStatus.Shuttle1PinSearchNoseP4 = result.PinSearchP2Value;
                    string noseMsg = $"执行 Nose 端 PinSearch 测试成功: {result.Message}，P3Value={result.PinSearchP1Value}，P4Value={result.PinSearchP2Value}，平均值={_mcuCmdService.NoseBasePinSearchValue}";
                    UILogService.AddSuccessLog(noseMsg);
                    HcGrowlExtensions.Success(noseMsg, waitTime: 5);
                }
            });
        }

        #region 通用循环测试框架 - 公共代码合并

        /// <summary>
        /// 通用的测试方法执行框架
        /// </summary>
        /// <param name="testName">测试名称</param>
        /// <param name="operationName">操作名称</param>
        /// <param name="loopExecutor">循环执行器</param>
        /// <param name="showSafetyConfirmation">是否显示安全确认</param>
        private async Task ExecuteTestMethodAsync(string testName, string operationName, Func<int, bool, int, CancellationToken, Task<bool>> loopExecutor, bool showSafetyConfirmation = true)
        {
            if (IsExecutingCommand)
                return;

            try
            {
                // 显示安全确认对话框
                if (showSafetyConfirmation)
                {
                    string loopInfo = LoopCount == -1 ? "无限循环" : $"{LoopCount}次";
                    if (!await ShowSafetyConfirmationAsync(operationName, loopInfo))
                        return;
                }

                // 验证前置条件
                if (!ValidateRobotConnection(operationName))
                    return;

                // 恢复RemainingLoopCount到初始值
                if (RemainingLoopCount != LoopCount)
                {
                    await UpdateUIAsync(() =>
                     {
                         RemainingLoopCount = LoopCount;
                         UILogService.AddLog($"循环次数已恢复到初始值: {LoopCount}");
                     });
                }

                // 设置执行状态
                IsExecutingCommand = true;
                _cancellationTokenSource = new CancellationTokenSource();

                // 重置已执行次数计数器并设置开始执行状态
                await UpdateUIAsync(() =>
                {
                    ExecutedCount = 0;
                    HasStartedExecution = true;
                    UILogService.AddLog($"重置已执行次数计数器: {ExecutedCount}");
                });

                // 显示开始信息
                string loopModeInfo = LoopCount == -1 ? "无限循环" : $"{LoopCount}次";
                UILogService.AddInfoLog($"开始执行机器人 {testName} 测试 - 循环模式: {loopModeInfo}");
                HcGrowlExtensions.Info($"正在执行 {testName} 测试 ({loopModeInfo})...", waitTime: 3);

                //  关键优化：将整个循环逻辑移到后台线程，彻底解决UI卡死问题
                await Task.Run(async () =>
                {
                    await ExecuteGenericLoopTestAsync(testName, loopExecutor, _cancellationTokenSource.Token);
                }, _cancellationTokenSource.Token);
            }
            catch (OperationCanceledException)
            {
                // 处理用户取消操作
                await UpdateUIAsync(() =>
                {
                    UILogService.AddWarningLog($"{testName}测试已被用户取消");
                    HcGrowlExtensions.Warning($"{testName}测试已取消", waitTime: 2);
                });
            }
            catch (Exception ex)
            {
                // 处理其他异常
                await UpdateUIAsync(() =>
                {
                    string errorMsg = $"{testName}测试异常: {ex.Message}";
                    UILogService.AddErrorLog(errorMsg);
                    HcGrowlExtensions.Error($"{testName}测试异常: {ex.Message}");
                    _logger.Error($"{testName}测试异常", ex);
                });
            }
            finally
            {
                // 清理资源
                await UpdateUIAsync(() =>
                {
                    GetCurrentRTZPosition();
                    UILogService.AddLog($"{testName}测试完成");
                    HasStartedExecution = false; // 重置开始执行状态
                });
                IsExecutingCommand = false;
                EnsureCancellationTokenSourceValid();
            }
        }

        /// <summary>
        /// 通用的安全确认对话框
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <param name="loopInfo">循环信息</param>
        /// <param name="showOnlyForMultiple">是否只在多次循环时显示</param>
        /// <returns>用户是否确认</returns>
        private Task<bool> ShowSafetyConfirmationAsync(string operationName, string loopInfo, bool showOnlyForMultiple = true)
        {
            // 只有多次循环或无限循环才显示确认对话框
            if (showOnlyForMultiple && LoopCount <= 1 && !LoopCount.Equals(-1))
            {
                return Task.FromResult(true);
            }

            var confirmResult = MessageBox.Show(
                "⚠️ 安全提示 ⚠️\n\n" +
                $"即将执行Robot {operationName}操作，请确认：\n\n" +
                "✓ Robot周围无人员和障碍物\n" +
                $"✓ 已检查Robot {operationName}运动路径，确保不会有碰撞的可能\n" +
                "✓ 供电插排按钮及时可断电使用作为紧急按钮使用\n\n" +
                $"执行次数：{loopInfo}\n\n" +
                $"确认执行 {operationName} 操作吗？",
                $"{operationName}安全确认",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning,
                MessageBoxResult.No);

            if (confirmResult != MessageBoxResult.Yes)
            {
                UILogService.AddLog($"用户取消了 {operationName} 操作");
                HcGrowlExtensions.Info($"已取消 {operationName} 操作", waitTime: 3);
                return Task.FromResult(false);
            }

            return Task.FromResult(true);
        }

        /// <summary>
        /// 通用的Robot连接状态验证
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateRobotConnection(string operationName)
        {
            // 检查命令服务
            if (_mcuCmdService == null)
            {
                string errorMsg = "命令服务为空，请检查依赖注入";
                UILogService.AddErrorLog(errorMsg);
                HcGrowlExtensions.Warning(errorMsg);
                _logger.Error(errorMsg);
                return false;
            }

            // 检查Robot连接状态
            if (!_mcuCmdService.Robot.IsConnected)
            {
                string errorMsg = "请先连接Robot设备";
                UILogService.AddErrorLog(errorMsg);
                HcGrowlExtensions.Warning(errorMsg);
                _logger.Error($"Robot未连接，无法执行{operationName}");
                return false;
            }

            return true;
        }

        /// <summary>
        /// 通用循环测试执行框架
        /// </summary>
        /// <param name="testName">测试名称</param>
        /// <param name="loopExecutor">循环执行器</param>
        /// <param name="cancellationToken">取消令牌</param>
        private async Task ExecuteGenericLoopTestAsync(
            string testName,
            Func<int, bool, int, CancellationToken, Task<bool>> loopExecutor,
            CancellationToken cancellationToken)
        {
            int currentLoop = 0;
            bool isInfiniteLoop = LoopCount == -1;
            int remainingCount = LoopCount; // 使用原始的LoopCount值，确保每次执行都是完整的循环次数

            try
            {
                //  优化：通用循环逻辑在后台线程运行，不阻塞UI
                while (remainingCount != 0 && !cancellationToken.IsCancellationRequested)
                {
                    currentLoop++;

                    //  优化：批量更新UI状态，减少UI线程调用频率
                    await UpdateUIAsync(() =>
                    {
                        string currentLoopInfo = isInfiniteLoop ? $"第{currentLoop}次" : $"第{currentLoop}次 (剩余{remainingCount}次)";
                        UILogService.AddLogAndIncreaseIndent($"=== {currentLoopInfo} {testName}测试开始 ===");
                    });

                    //  优化：及时检查取消状态
                    if (cancellationToken.IsCancellationRequested)
                        break;

                    // 执行具体的测试逻辑
                    bool shouldContinue = await loopExecutor(currentLoop, isInfiniteLoop, remainingCount, cancellationToken);
                    if (!shouldContinue)
                        break;

                    //  优化：批量更新循环完成状态和已执行次数
                    await UpdateUIAsync(() =>
                    {
                        string currentLoopInfo = isInfiniteLoop ? $"第{currentLoop}次" : $"第{currentLoop}次 (剩余{remainingCount}次)";
                        UILogService.DecreaseIndentAndAddSuccessLog($"=== {currentLoopInfo} {testName}测试完成 ===");

                        // 更新已执行次数计数器
                        ExecutedCount = currentLoop;
                        UILogService.AddLog($"已执行次数: {ExecutedCount}");
                    });

                    // 更新循环计数
                    if (!isInfiniteLoop && remainingCount > 0)
                    {
                        remainingCount--;
                        await UpdateUIAsync(() =>
                        {
                            RemainingLoopCount = remainingCount;
                            UILogService.AddLog($"剩余循环次数: {remainingCount}");
                        });
                    }

                    //  优化：使用可取消的延迟
                    if (remainingCount != 0 && !cancellationToken.IsCancellationRequested)
                    {
                        await UpdateUIAsync(() => UILogService.AddLog("等待2秒后开始下一次循环..."));
                        await Task.Delay(2000, cancellationToken);
                    }
                }

                // 显示完成信息
                await UpdateUIAsync(() =>
                {
                    string completionMessage = cancellationToken.IsCancellationRequested
                        ? $"{testName}测试已取消 (已完成{Math.Max(0, currentLoop - 1)}次)"
                        : $"{testName}测试全部完成 (共执行{currentLoop}次)";
                    UILogService.AddInfoLog(completionMessage);
                    HcGrowlExtensions.Info(completionMessage, waitTime: 3);
                });
            }
            catch (OperationCanceledException)
            {
                // 正常的取消操作，重新抛出
                throw;
            }
            catch (Exception ex)
            {
                // 记录异常
                await UpdateUIAsync(() =>
                {
                    UILogService.AddErrorLog($"{testName}循环执行异常: {ex.Message}");
                    _logger.Error($"{testName}循环执行异常", ex);
                });
            }
        }

        /// <summary>
        ///  优化：统一的UI更新方法，确保所有UI操作在UI线程上执行
        /// </summary>
        private async Task UpdateUIAsync(Action uiAction)
        {
            if (Application.Current?.Dispatcher != null)
            {
                await Application.Current.Dispatcher.InvokeAsync(uiAction);
            }
        }

        #endregion 通用循环测试框架 - 公共代码合并

        #endregion PinSearch 测试

        #region 开始、停止：晶圆搬运测试命令：TRZ各轴Nose、Smooth端基本测试【注意安全】

        /// <summary>
        /// 晶圆搬运测试命令：TRZ各轴Nose、Smooth端基本测试
        /// </summary>
        [RelayCommand]
        private async Task TrasferWaferTest()
        {
            if (IsExecutingCommand)
                return;

            try
            {
                // 记录当前Robot连接状态
                UILogService.AddLog($"执行搬运测试，Robot连接状态: {_mcuCmdService.Robot?.IsConnected}，服务实例: {_mcuCmdService.GetType().FullName}");

                // 检查设备连接状态
                if (_mcuCmdService == null)
                {
                    _strMsg = "命令服务为空，请检查依赖注入";
                    CommandResult = _strMsg;
                    HcGrowlExtensions.Warning(_strMsg);
                    UILogService.AddLog(_strMsg);
                    _logger.Error(_strMsg);
                    return;
                }

                if (!_mcuCmdService.Robot.IsConnected)
                {
                    _strMsg = "请先连接Robot设备";
                    CommandResult = _strMsg;
                    HcGrowlExtensions.Warning(_strMsg);
                    UILogService.AddLog(_strMsg);
                    _logger.Error($"Robot未连接，无法执行搬运测试");
                    return;
                }

                #region TRZ各轴Nose、Smooth端基本测试

                // TRZ 轴测试

                // 设置开始执行命令状态
                IsExecutingCommand = true;
                _strMsg = "正在执行TRZ 轴基本测试测试...";
                CommandResult = _strMsg;
                HcGrowlExtensions.Info(_strMsg);
                UILogService.AddLog(_strMsg);
                // 设置延迟时间
                const int delayTime = 500;

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                // R轴测试 - Nose端
                UILogService.AddLog("开始执行 Nose端 R轴测试...");
                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                await Task.Delay(delayTime); // 等待动作完成

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);
                UILogService.AddLog("✅ Nose端 R轴测试完成");

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                // R轴测试 - Smooth端
                UILogService.AddLog("开始执行 Smooth端 R轴测试...");
                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveRAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);
                UILogService.AddLog("✅ Smooth端 R轴测试完成");

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                // T轴测试 - Nose端
                UILogService.AddLog("开始执行 Nose端 T轴测试...");
                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToPositionAsync(100, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);
                UILogService.AddLog("✅ Nose端 T轴测试完成");

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                // T轴测试 - Smooth端
                UILogService.AddLog("开始执行 Smooth端 T轴测试...");
                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToLocationAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveTAxisToPositionAsync(100, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);
                UILogService.AddLog("✅ Smooth端 T轴测试完成");

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                // Z轴测试 - Nose端
                UILogService.AddLog("开始执行 Nose端 Z轴测试...");
                await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.Cassette, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.Cassette, 25, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingTop, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Nose, EnuLocationStationType.CoolingBottom, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);
                UILogService.AddLog("✅ Nose端 Z轴测试完成");

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                // Z轴测试 - Smooth端
                UILogService.AddLog("开始执行 Smooth端 Z轴测试...");
                await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette, 25, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToGetPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingTop, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);

                // 检查是否取消操作
                if (_cancellationTokenSource?.IsCancellationRequested == true)
                {
                    UILogService.AddLog("测试已取消");
                    return;
                }

                await _mcuCmdService.MoveZAxisToPutPositionAsync(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingBottom, 1, _cancellationTokenSource.Token);
                await Task.Delay(delayTime);
                UILogService.AddLog("✅ Smooth端 Z轴测试完成");

                UILogService.AddLog("✅ TRZ轴基本测试全部完成");

                #endregion TRZ各轴Nose、Smooth端基本测试
            }
            catch (OperationCanceledException)
            {
                _strMsg = "测试已取消";
                CommandResult = _strMsg;
                UILogService.AddLog(_strMsg);
            }
            catch (Exception ex)
            {
                _strMsg = $"搬运Wafer测试异常: {ex.Message}";
                CommandResult = _strMsg;
                HcGrowlExtensions.Error(_strMsg);
                UILogService.AddLog($"❌ {_strMsg}");
                _logger.Error($"搬运Wafer测试异常", ex);
            }
            finally
            {
                GetCurrentRTZPosition();
                IsExecutingCommand = false;
                _strMsg = "搬运Wafer测试操作完成";
                UILogService.AddLog(_strMsg);
                HcGrowlExtensions.Info(_strMsg);
            }
        }

        /// <summary>
        /// 执行全部测试命令：TRZ各轴Nose、Smooth端基本测试
        /// </summary>
        [RelayCommand]
        private async Task ExecuteAllTests()
        {
            if (IsTestRunning)
                return;

            try
            {
                // 初始化取消令牌
                _cancellationTokenSource = new CancellationTokenSource();
                IsTestRunning = true;

                UILogService.AddLog("开始执行全部TRZ轴测试...");

                // 执行测试
                await TrasferWaferTest();
            }
            catch (OperationCanceledException)
            {
                UILogService.AddLog("测试已取消");
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"执行测试异常: {ex.Message}");
            }
            finally
            {
                GetCurrentRTZPosition();
                IsTestRunning = false;
                _cancellationTokenSource = null;
            }
        }

        /// <summary>
        /// 停止测试命令
        /// </summary>
        [RelayCommand]
        private void StopTest()
        {
            if (!IsTestRunning || _cancellationTokenSource == null)
                return;

            try
            {
                _cancellationTokenSource.Cancel();
                UILogService.AddLog("正在停止测试...");
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"停止测试异常: {ex.Message}");
            }
        }

        #endregion 开始、停止：晶圆搬运测试命令：TRZ各轴Nose、Smooth端基本测试【注意安全】

        #region 复制消息到剪贴板命令

        /// <summary>
        /// 复制消息到剪贴板命令
        /// </summary>
        [RelayCommand]
        private void CopyMessageToClipboard(string message)
        {
            try
            {
                Clipboard.SetText(message);
                UILogService.AddLog("消息已复制到剪贴板");
            }
            catch (Exception ex)
            {
                UILogService.AddLog($"复制消息到剪贴板异常: {ex.Message}");
            }
        }

        #endregion 复制消息到剪贴板命令

        #endregion 命令

        #region 取消令牌管理

        /// <summary>
        /// 确保取消令牌源有效，如果已取消或释放则重新创建
        /// </summary>
        private void EnsureCancellationTokenSourceValid()
        {
            if (_cancellationTokenSource == null || _cancellationTokenSource.Token.IsCancellationRequested)
            {
                _cancellationTokenSource?.Dispose();
                _cancellationTokenSource = new();
            }
        }

        #endregion 取消令牌管理
    }
}