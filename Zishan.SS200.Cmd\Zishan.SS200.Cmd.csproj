﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <PackageIcon></PackageIcon>
    <ApplicationIcon>Assets\Images\logo.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Compile Remove="Docs\**\*.cs" />
    <EmbeddedResource Remove="Docs\**\*.cs" />
    <None Include="Docs\**\*.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Remove=".cursorignore" />
    <None Remove=".cursorindexingignore" />
    <None Remove=".cursorrc" />
    <None Remove=".cursorsettings" />
    <None Remove=".gitignore" />
    <None Remove="Assets\Images\CalibrationContent.png" />
    <None Remove="Assets\Images\plate.png" />
    <None Remove="Config.ini" />
    <None Remove="Configs\AlarmInfo\MotorAlarmInfo.json" />
    <None Remove="Configs\AlarmInfo\RobotAlarmInfo.json" />
    <None Remove="Configs\AlarmInfo\SHTLAlarmInfo.json" />
    <None Remove="Configs\Log4netConfig\log4net-development.config" />
    <None Remove="Configs\Log4netConfig\log4net-optimized.config" />
    <None Remove="Configs\Log4netConfig\log4net-production.config" />
    <None Remove="Configs\Log4netConfig\log4net.config" />
    <None Remove="Configs\LoopConfigTest\BatchCommands.json" />
    <None Remove="Configs\Recipe\IR400RecipeNames.json" />
    <None Remove="Configs\SS200\AlarmCode\ChamberA\ChamberAErrorCodes.json" />
    <None Remove="Configs\SS200\AlarmCode\ChamberB\ChamberBErrorCodes.json" />
    <None Remove="Configs\SS200\AlarmCode\Robot\RobotErrorCodes.json" />
    <None Remove="Configs\SS200\AlarmCode\Shuttle\ShuttleErrorCodes.json" />
    <None Remove="Configs\SS200\SubsystemConfigure\ChamberA\ChaConfigParameters.json" />
    <None Remove="Configs\SS200\SubsystemConfigure\MainSystem\MainSystemConfigParameters.json" />
    <None Remove="Configs\SS200\SubsystemConfigure\Robot\RobotConfigureSettings.json" />
    <None Remove="Configs\SS200\SubsystemConfigure\Shuttle\ShuttleConfigParameters.json" />
    <None Remove="README-CURSOR.md" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Docs\Examples\ErrorCodeInfoParserExample.cs" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\logo.ico" />
    <Content Include="Configs\AlarmInfo\MotorAlarmInfo.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\ErrorCodeInfo\RobotAlarmInfo.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\ErrorCodeInfo\SHTLAlarmInfo.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\CmdParameter\ChamberBParameter.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\Config.ini">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\Log4netConfig\log4net-development.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\Log4netConfig\log4net-optimized.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\Log4netConfig\log4net-production.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\Log4netConfig\log4net.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\CmdParameter\ChamberAParameter.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\CmdParameter\RobotParameter.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\IoConfigInfo\McuDeviceCoilName.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\CmdParameter\ShuttleParameter.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\LoopConfigTest\BatchCommands.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\Recipe\IR400RecipeNames.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\AlarmCode\ChamberA\ChamberAErrorCodes.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\AlarmCode\ChamberB\ChamberBErrorCodes.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\AlarmCode\Robot\RobotErrorCodes.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\AlarmCode\Shuttle\ShuttleErrorCodes.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\SubsystemConfigure\ChamberA\ChaConfigParameters.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\SubsystemConfigure\ChamberB\ChbConfigParameters.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\SubsystemConfigure\MainSystem\MainSystemConfigParameters.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\SubsystemConfigure\Robot\RobotConfigureSettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\SubsystemConfigure\Robot\RobotPositionParameters.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Configs\SS200\SubsystemConfigure\Shuttle\ShuttleConfigParameters.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0" />
    <PackageReference Include="gong-wpf-dragdrop" Version="4.0.0" />
    <PackageReference Include="HandyControl" Version="3.5.1" />
    <PackageReference Include="ini-parser-netstandard" Version="2.5.3" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NModbus" Version="3.0.81" />
    <PackageReference Include="Prism.DryIoc" Version="8.1.97" />
    <PackageReference Include="SqlSugarCore" Version="5.1.4.195" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.37" />
    <PackageReference Include="Wu" Version="1.0.7" />
    <PackageReference Include="Wu.Wpf" Version="1.0.11" />
    <PackageReference Include="Wu.Wpf.ControlLibrary" Version="1.0.1" />
    <PackageReference Include="log4net" Version="3.1.0" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
    <PackageReference Include="Prism.Wpf" Version="8.1.97" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Assets\Images\CalibrationContent.png" />
    <Resource Include="Assets\Images\plate.png">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <None Update="Assets\Images\logo.ico">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
    <None Update="Configs\LoopConfigTest\BatchCommands_sample.json">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Docs\Configs\SS200\SubsystemConfigure\ChamberB\" />
    <Folder Include="Docs\Configs\SS200\SubsystemConfigure\Robot\" />
    <Folder Include="Enums\SS200\AlarmCode\ChamberB\" />
    <Folder Include="Enums\SS200\SubsystemConfigure\ChamberB\" />
    <Folder Include="Enums\SS200\SubSystemStatus\" />
    <Folder Include="Models\SS200\SubSystemStatus\Shuttle\" />
  </ItemGroup>
</Project>