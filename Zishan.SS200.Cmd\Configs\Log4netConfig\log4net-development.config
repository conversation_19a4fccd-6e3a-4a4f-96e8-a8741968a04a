<?xml version="1.0" encoding="utf-8" ?>
<!-- 开发环境配置 - 详细日志，包含DEBUG信息 -->
<log4net>
	<!-- 异步错误日志Appender -->
	<appender name="asyncErrorAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="512" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="ERROR" />
		</evaluator>
		<appender-ref ref="errorAppender" />
	</appender>

	<!-- 异步警告日志Appender -->
	<appender name="asyncWarnAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="256" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="WARN" />
		</evaluator>
		<appender-ref ref="warnAppender" />
	</appender>

	<!-- 异步信息日志Appender -->
	<appender name="asyncInfoAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="256" />
		<lossy value="false" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="INFO" />
		</evaluator>
		<appender-ref ref="infoAppender" />
	</appender>

	<!-- 异步调试日志Appender -->
	<appender name="asyncDebugAppender" type="log4net.Appender.AsyncAppender">
		<bufferSize value="128" />
		<lossy value="true" />
		<evaluator type="log4net.Core.LevelEvaluator">
			<threshold value="DEBUG" />
		</evaluator>
		<appender-ref ref="debugAppender" />
	</appender>

	<!-- 控制台输出Appender -->
	<appender name="consoleAppender" type="log4net.Appender.ConsoleAppender">
		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
		</layout>
	</appender>

	<!-- 基础错误日志Appender -->
	<appender name="errorAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="ERROR" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\err.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="10" />
		<param name="MaximumFileSize" value="10MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础警告日志Appender -->
	<appender name="warnAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="WARN" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\warn.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="10" />
		<param name="MaximumFileSize" value="10MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础信息日志Appender -->
	<appender name="infoAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="INFO" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\info.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="10" />
		<param name="MaximumFileSize" value="10MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 基础调试日志Appender -->
	<appender name="debugAppender" type="log4net.Appender.RollingFileAppender">
		<filter type="log4net.Filter.LevelMatchFilter">
			<levelToMatch value="DEBUG" />
		</filter>
		<filter type="log4net.Filter.DenyAllFilter" />
		<file value="Logs\debug.log" />
		<encoding value="utf-8" />
		<preserveLogFileNameExtension value="true" />
		<appendToFile value="true" />
		<rollingStyle value="Composite" />
		<datePattern value="yyyyMMdd" />
		<staticLogFileName value="false" />

		<param name="MaxSizeRollBackups" value="5" />
		<param name="MaximumFileSize" value="20MB" />

		<layout type="log4net.Layout.PatternLayout">
			<conversionPattern value="%date [%thread] %-5level %logger{1} - %message%newline" />
		</layout>
		<lockingModel type="log4net.Appender.MinimalLockDeleteEmpty" />
	</appender>

	<!-- 根日志配置 - 开发环境使用DEBUG级别 -->
	<root>
		<level value="DEBUG" />
		<appender-ref ref="asyncErrorAppender" />
		<appender-ref ref="asyncWarnAppender" />
		<appender-ref ref="asyncInfoAppender" />
		<appender-ref ref="asyncDebugAppender" />
		<appender-ref ref="consoleAppender" />
	</root>
</log4net>
