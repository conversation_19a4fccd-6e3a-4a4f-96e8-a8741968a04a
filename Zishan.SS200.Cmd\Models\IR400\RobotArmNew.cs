using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Extensions;
using Zishan.SS200.Cmd.Services;
using Zishan.SS200.Cmd.ViewModels;
using Zishan.SS200.Cmd.Views.Dialogs;

namespace Zishan.SS200.Cmd.Models.IR400
{
    /// <summary>
    /// RobotArmNose 类表示机械臂, 用于移动 Wafer
    /// </summary>
    public class RobotArmNew : BContainer
    {
        private string _strMsg = string.Empty;

        private readonly TransferWaferViewModel iR400ViewModel;

        public RobotArmNew(EnuChamberName name, int processTime = 500, int capacity = 1, EnuArmFetchSide enuArmFetchSide = EnuArmFetchSide.Nose, TransferWaferViewModel iR400ViewModel = null) : base(name, processTime, capacity)
        {
            ArmFetchSide = enuArmFetchSide;
            this.iR400ViewModel = iR400ViewModel;
        }

        /// <summary>
        /// 从来源腔体中取出 Wafer 并放入目标腔体。如果目标腔体是冷却腔体，并且冷却腔体已满，那么就先把冷却好的Wafer放回到Cassette中。
        /// </summary>
        /// <param name="from"></param>
        /// <param name="fromSlotId"></param>
        /// <param name="to"></param>
        /// <param name="toSlotId"></param>
        /// <param name="cassette"></param>
        /// <param name="enuArmFetchSide">机械手臂抓取面：A面 Nose端、B面 Smooth端</param>
        /// <returns></returns>
        public async Task<bool> TransferWafer(BContainer from, int fromSlotId, BContainer to, int toSlotId, Cassette cassette, EnuArmFetchSide enuArmFetchSide)
        {
            if (from == null || to == null)
            {
                _strMsg = "from或to腔体不能为空，请检查！";
                UILogService.AddLog(_strMsg);
                Console.WriteLine(_strMsg);
                return false;
            }

            var blResult = false;

            if ((!from.HasUnfinishedWafer()) && (!to.HasUnfinishedWafer()))
            {
                _strMsg = $"源、目的Wafer数量不能同时为0,from:{from.ToString()},To:{to.ToString()}";
                UILogService.AddLog(_strMsg);
                Console.WriteLine(_strMsg);
                return false;
            }

            EnuLocationStationType sourceStationType = EnuLocationStationType.None;
            EnuLocationStationType targetStationType = EnuLocationStationType.None;
            // 获取机械臂端口类型
            EnuRobotEndType endType = EnuRobotEndType.Nose;

            // 获取源站点类型和目标站点类型

            switch (from.ChamberName)
            {
                case EnuChamberName.Cassette:
                    sourceStationType = EnuLocationStationType.Cassette;
                    break;

                case EnuChamberName.CHA:
                    sourceStationType = EnuLocationStationType.ChamberA;
                    break;

                case EnuChamberName.CHB:
                    sourceStationType = EnuLocationStationType.ChamberB;
                    break;

                case EnuChamberName.CHC:
                    // CHC暂时映射到CoolingTop
                    sourceStationType = EnuLocationStationType.CoolingTop;
                    UILogService.AddWarningLog($"CHC腔体暂时映射到CoolingTop位置类型");
                    break;

                case EnuChamberName.Cooling:
                    sourceStationType = EnuLocationStationType.CoolingBottom;
                    break;

                default:
                    UILogService.AddWarningLog($"{from.ChamberName}源参数设置错误，没有对应的枚举映射");
                    break;
            }

            switch (to.ChamberName)
            {
                case EnuChamberName.Cassette:
                    targetStationType = EnuLocationStationType.Cassette;
                    break;

                case EnuChamberName.CHA:
                    targetStationType = EnuLocationStationType.ChamberA;
                    break;

                case EnuChamberName.CHB:
                    targetStationType = EnuLocationStationType.ChamberB;
                    break;

                case EnuChamberName.CHC:
                    // CHC暂时映射到CoolingTop
                    targetStationType = EnuLocationStationType.CoolingTop;
                    UILogService.AddWarningLog($"CHC腔体暂时映射到CoolingTop位置类型");
                    break;

                case EnuChamberName.Cooling:
                    targetStationType = EnuLocationStationType.CoolingBottom;
                    break;

                default:
                    UILogService.AddWarningLog($"{to.ChamberName}目的参数设置错误，没有对应的枚举映射");
                    break;
            }

            switch (enuArmFetchSide)
            {
                case EnuArmFetchSide.Nose:
                    endType = EnuRobotEndType.Nose;
                    break;

                case EnuArmFetchSide.Smooth:
                    endType = EnuRobotEndType.Smooth;
                    break;
            }

            //转换后检查上面的枚举类型是否正确，不能None
            if (sourceStationType == EnuLocationStationType.None || targetStationType == EnuLocationStationType.None)
            {
                _strMsg = $"源或目标站点类型转换错误，请检查from:{from.ChamberName},to:{to.ChamberName}的枚举映射";
                UILogService.AddLog(_strMsg);
                //return false;
            }

            if (from != null && !from.IsEmpty() && !to.IsFull())
            {
                var fromCassette = from as Cassette;
                if (fromCassette != null && !fromCassette.HasUnfinishedWafer()) return false;//判断源是否是Cassette,而且有未处理的Wafer，否则返回

                if (!from.IsProcessed)
                {
                    var isProcessed = from.ProcessWafers(); // 等待处理完 Wafer
                }

                if (!(this.IsFull() && from is not RobotArmNew))
                {
                    List<Wafer> GetWafers = new List<Wafer>();

                    if (!(this.IsFull() && from is RobotArmNew))
                    {
                        //1、执行Get指令

                        iR400ViewModel.ResponseCmd = string.Empty;

                        Application.Current.Dispatcher.Invoke(() =>
                       {
                           //取Cassette动画
                           iR400ViewModel.Slot = fromSlotId;
                           iR400ViewModel.CurChamberGoAction = EnuChamberGoAction.Get;
                           iR400ViewModel.ToChamberAngle = from.ChamberName;
                       });

                        (bool Success, string Message) result = (false, string.Empty);
                        try
                        {
                            // 获取S200McuCmdService实例
                            //var cmdService = S200McuCmdService.Instance;

                            // 使用RobotWaferOperationsExtensions中的TrasferWaferAsync方法执行晶圆传输
                            UILogService.AddInfoLog($"🤖 GetWafer动作: 从{sourceStationType}(Slot:{fromSlotId}) 使用{endType}端抓取晶圆 -> 目标位置:{sourceStationType}【{from.ChamberName}】");
                            if (!Golbal.IsDevDebug)
                            {
                                // 🔥 性能监控：调用GetWaferAsync耗时，搬运操作计时
                                await StopwatchHelper.MeasureAsync(async () =>
                                    {
                                        result = await iR400ViewModel.McuCmdService.GetWaferAsync(endType, sourceStationType, fromSlotId, CancellationToken.None); //暂时注释掉实际动作
                                        // await Task.CompletedTask;
                                        // result = (true, $"模拟GetWaferAsync：endType：{endType}；sourceStationType：{sourceStationType}；fromSlotId：{fromSlotId}");
                                    }, $"调用GetWaferAsync耗时 ,endType：{endType}；sourceStationType：{sourceStationType}；fromSlotId：{fromSlotId}", warnThresholdMs: 10000);
                            }

                            result.Success = true;
                            result.Message = $"***从{from.ChamberName}获取晶圆成功，Slot: {fromSlotId}";

                            if (result.Success)
                            {
                                UILogService.AddSuccessLog($"晶圆搬运成功: {result.Message}");
                                blResult = true;
                            }
                            else
                            {
                                UILogService.AddErrorLog($"晶圆搬运失败: {result.Message}");
                                blResult = false;
                            }
                        }
                        catch (Exception ex)
                        {
                            UILogService.AddErrorLog($"晶圆搬运异常: {ex.Message}");
                            //ExcuteResult = false;
                            blResult = false;
                        }

                        // 这里替换实现Robot搬运命令调用：@RobotWaferOperationsExtensions.cs

                        if (blResult)
                        {
                            _strMsg = $"执行命令：{iR400ViewModel.RequestCmdDesc} 对应PLC命令【{iR400ViewModel.RequestCmd}】，返回：{blResult}，返回信息：{result.Message}";
                            UILogService.AddLog(_strMsg);

                            //指令执行成功，获取指定的WaferID
                            Application.Current.Dispatcher.Invoke(() =>
                            {
                                GetWafers = from.RemoveWafers(1, EnuWaferRemoveMode.specified, fromSlotId); // 获取处理好的Wafer
                            });
                            _strMsg = $"{this.ChamberName}机械臂【{enuArmFetchSide}端】正在抓取{from.ChamberName} Wafer";
                            UILogService.AddLog(_strMsg);
                            Console.WriteLine(_strMsg);
                            blResult = this.AddWafers(GetWafers, out _strMsg);

                            _strMsg = string.Empty;
                            switch (from.ChamberName)
                            {
                                case EnuChamberName.CHA:

                                    //判断Norse端是否有待做工艺的Wafer，有不关门，否则关门

                                    if (!iR400ViewModel.CurPLcsignalSimulation.RobotNorthHaseWafer)
                                    {
                                        if (Golbal.IsDevDebug)
                                        {
                                            // 测试环境
                                        }
                                        else
                                        {
                                            // 正式环境，直接发送关门指令
                                            //await Task.Delay(1000);
                                        }
                                        _strMsg = $"已发送关门指令： {from.ChamberName}【Get Slot:{fromSlotId}】";

                                        UILogService.AddLog(_strMsg);

                                        //检测门是否关闭
                                        _strMsg = $"{from.ChamberName}->{to.ChamberName}【Get Slot:{fromSlotId}】，发送CHA关门指令后，检测门关闭状态超时,请确认后重试！";
                                        bool isChaDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.CHADoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                        if (!isChaDoorClosed)
                                        {
                                            _strMsg = $"{from.ChamberName}门未关闭，无法执行下去,已退出程序";
                                            UILogService.AddLog(_strMsg);
                                            MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                            return false;
                                        }
                                        _strMsg = $"已检测门已关闭 {from.ChamberName}";
                                    }

                                    break;

                                case EnuChamberName.CHB:

                                    // 判断Norse端是否有待做工艺的Wafer，有不关门，否则关门
                                    if (!iR400ViewModel.CurPLcsignalSimulation.RobotNorthHaseWafer)
                                    {
                                        if (Golbal.IsDevDebug)
                                        {
                                            // 测试环境
                                        }
                                        else
                                        {
                                            // 正式环境，直接发送关门指令
                                            //await Task.Delay(1000);
                                        }
                                        _strMsg = $"已发送关门指令： {from.ChamberName}【Get Slot:{fromSlotId}】";

                                        UILogService.AddLog(_strMsg);

                                        //检测门是否关闭
                                        _strMsg = $"{from.ChamberName}->{to.ChamberName}【Get  Slot:{fromSlotId}】，发送CHB关门指令后，检测门关闭状态超时,请确认后重试！";
                                        bool isChbDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.CHBDoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);

                                        if (!isChbDoorClosed)
                                        {
                                            _strMsg = $"{from.ChamberName}门未关闭，无法执行下去,已退出程序";
                                            UILogService.AddLog(_strMsg);
                                            MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                            return false;
                                        }
                                        _strMsg = $"已检测门已关闭 {from.ChamberName}";
                                    }
                                    break;

                                case EnuChamberName.CHC:
                                    // 判断Norse端是否有待做工艺的Wafer，有不关门，否则关门
                                    if (!iR400ViewModel.CurPLcsignalSimulation.RobotNorthHaseWafer)
                                    {
                                        if (Golbal.IsDevDebug)
                                        {
                                            // 测试环境
                                        }
                                        else
                                        {
                                            // 正式环境，直接发送关门指令
                                            //await Task.Delay(1000);
                                        }
                                        _strMsg = $"已发送关门指令： {from.ChamberName}【Get Slot:{fromSlotId}】";

                                        UILogService.AddLog(_strMsg);

                                        //检测门是否关闭
                                        _strMsg = $"{from.ChamberName}->{to.ChamberName}【Get  Slot:{fromSlotId}】，发送CHC关门指令后，检测门关闭状态超时,请确认后重试！";
                                        bool isChcDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.CHCDoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);

                                        if (!isChcDoorClosed)
                                        {
                                            _strMsg = $"{from.ChamberName}门未关闭，无法执行下去,已退出程序";
                                            UILogService.AddLog(_strMsg);
                                            MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                            return false;
                                        }
                                        _strMsg = $"已检测门已关闭 {from.ChamberName}";
                                    }

                                    break;

                                case EnuChamberName.Cooling:
                                    break;

                                case EnuChamberName.Cassette:
                                    if (Golbal.IsDevDebug)
                                    {
                                        // 测试环境
                                    }
                                    else
                                    {
                                        // 正式环境，直接发送关门指令
                                    }

                                    //检测门是否关闭
                                    _strMsg = $"{from.ChamberName}->{to.ChamberName}【Get  Slot:{fromSlotId}】，发送LoadLock关门指令后，检测门关闭状态超时,请确认后重试！";
                                    bool isLoadLockDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.LoadLockDoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                    if (!isLoadLockDoorClosed)
                                    {
                                        _strMsg = $"{from.ChamberName}门未关闭，无法执行下去,已退出程序";
                                        UILogService.AddLog(_strMsg);
                                        MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                        return false;
                                    }
                                    _strMsg = $"已检测门已关闭 {from.ChamberName}";
                                    break;

                                case EnuChamberName.LoadLock:
                                    break;

                                case EnuChamberName.RobotArmNose:
                                    break;

                                case EnuChamberName.RobotArmSmooth:
                                    break;

                                case EnuChamberName.Home:
                                    break;

                                case EnuChamberName.Host:
                                    break;

                                case EnuChamberName.Buffer:
                                    break;
                            }
                            if (!string.IsNullOrEmpty(_strMsg))
                            {
                                UILogService.AddLog(_strMsg + "【Get】");
                            }
                        }
                        else
                        {
                            MessageBox.Show(_strMsg, "Get指令执行失败，将被终止，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    else
                    {
                        //直接引用已有的Wafers
                        var getWafers = this.LeftWaferAction.Wafers.ToList();
                        getWafers.AddRange(this.RightWaferAction.Wafers.ToList());
                        GetWafers = getWafers;
                        blResult = true;
                    }

                    if (blResult)
                    {
                        if (from is CoolingChamber || from is Chamber)
                        {
                            from.StopTimer(); // Wafer离开冷却腔体或腔体后，立马停止冷却计时
                        }

                        _strMsg = $"{this.ChamberName}机械臂【{enuArmFetchSide}端】已抓取左边：{string.Join(";", LeftWaferAction.Wafers)}，右边：{string.Join(";", RightWaferAction.Wafers)}，总共{LeftWaferAction.Wafers.Count + RightWaferAction.Wafers.Count}片Wafer";
                        UILogService.AddLog(_strMsg);
                        Console.WriteLine(_strMsg);

                        if (to is not RobotArmNew)
                        {
                            if (from is not RobotArmNew)
                            {
                                if (from is not CoolingChamber)
                                {
                                    // 回原点指令相关逻辑已注释
                                }
                                else
                                {
                                    // 回原点指令相关逻辑已注释
                                }
                            }

                            if (from is RobotArmNew)
                            {
                                if (to is Cassette cassette1)
                                {
                                    //做标记
                                    GetWafers.ForEach(w => { w.IsFinisheded = true; w.WaferStatus = EnuWaferStatus.AutoFinished; });
                                }
                            }

                            //放回Cassettes提前标识完成
                            if (from is CoolingChamber)
                            {
                                if (to is Cassette cassette1)
                                {
                                    //做标记
                                    GetWafers.ForEach(w => { w.IsFinisheded = true; w.WaferStatus = EnuWaferStatus.AutoFinished; });
                                }
                            }

                            //blResult = to.AddWafers(GetWafers, out _strMsg);
                            if (true)
                            {
                                //this.RemoveWafers(1, EnuWaferRemoveMode.Front, -1);

                                //3、执行PUT指令

                                iR400ViewModel.ResponseCmd = string.Empty;

                                Application.Current.Dispatcher.Invoke(() =>
                                {
                                    //取Cassette动画
                                    if (to.ChamberName != EnuChamberName.Cooling)
                                    {
                                        iR400ViewModel.CurChamberGoAction = EnuChamberGoAction.Put;
                                    }
                                    else
                                    {
                                        iR400ViewModel.CurChamberGoAction = EnuChamberGoAction.None;
                                    }
                                    iR400ViewModel.Slot = fromSlotId;
                                    iR400ViewModel.ToChamberAngle = to.ChamberName;
                                });

                                (bool Success, string Message) result = (false, string.Empty);
                                try
                                {
                                    // 获取S200McuCmdService实例
                                    //var cmdService = S200McuCmdService.Instance;

                                    // 使用RobotWaferOperationsExtensions中的TrasferWaferAsync方法执行晶圆传输
                                    UILogService.AddInfoLog($"🤖 PutWafer动作: 将晶圆放入{targetStationType}(Slot:{toSlotId}) 使用{endType}端 -> 目标位置:{targetStationType}【{to.ChamberName}】");
                                    if (targetStationType == EnuLocationStationType.None)//目标站点类型转换错误None，实际是机械臂的Nose端或者 Smooth端
                                    {
                                        UILogService.AddErrorLog($"目标站点类型转换错误，请检查to:{to.ChamberName}的枚举映射");
                                        //return false;
                                    }

                                    if (!Golbal.IsDevDebug)
                                    {
                                        // 🔥 性能监控：调用PutWaferAsync耗时，搬运操作计时
                                        await StopwatchHelper.MeasureAsync(async () =>
                                        {
                                            result = await iR400ViewModel.McuCmdService.PutWaferAsync(endType, targetStationType, toSlotId, CancellationToken.None); //暂时注释掉实际动作
                                            // await Task.CompletedTask;
                                            // result = (true, $"模拟PutWaferAsync：endType：{endType}；sourceStationType：{sourceStationType}；fromSlotId：{fromSlotId}");
                                        }, $"调用PutWaferAsync耗时 ,endType：{endType}；sourceStationType：{sourceStationType}；fromSlotId：{fromSlotId}", warnThresholdMs: 10000);
                                    }

                                    result.Success = true;
                                    result.Message = $"***将晶圆放入{to.ChamberName}成功，Slot: {toSlotId}";

                                    if (result.Success)
                                    {
                                        UILogService.AddSuccessLog($"晶圆搬运成功: {result.Message}");
                                        blResult = true;
                                        //ExcuteResult = true;
                                    }
                                    else
                                    {
                                        UILogService.AddErrorLog($"晶圆搬运失败: {result.Message}");
                                        //ExcuteResult = false;
                                        blResult = false;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    UILogService.AddErrorLog($"晶圆搬运异常: {ex.Message}");
                                    //ExcuteResult = false;
                                    blResult = false;
                                }

                                //Task.Delay(3000).Wait();
                                /*var tupleResult = await ProcessCustomCommand(iR400ViewModel.RequestCmd, iR400ViewModel.RequestCmdDesc);
                                blResult = tupleResult.Item1;
                                strResponse = tupleResult.Item2;
                                //重置后，第二次执行会返回这个值，原因可能指令没有返回，溢出了
                                if (strResponse == "IsCancellationRequested" || strResponse.Contains("A task was canceled"))
                                {
                                    return false;
                                }*/

                                if (blResult)
                                {
                                    _strMsg = $"执行命令：{iR400ViewModel.RequestCmdDesc} 对应PLC命令【 {iR400ViewModel.RequestCmd} 】，返回：{blResult}，返回信息：strResponse";
                                    UILogService.AddLog(_strMsg);
                                    _strMsg = $"{this.ChamberName}机械臂【{enuArmFetchSide}端】已把抓取{string.Join(";", GetWafers)}，总共{GetWafers.Count}片Wafer放到{to.ChamberName}中";
                                    UILogService.AddLog(_strMsg);
                                    Console.WriteLine(_strMsg);

                                    blResult = to.AddWafers(GetWafers, out _strMsg);
                                    this.RemoveWafers(1, EnuWaferRemoveMode.Front);

                                    _strMsg = string.Empty;
                                    switch (to.ChamberName)
                                    {
                                        case EnuChamberName.CHA:
                                            if (Golbal.IsDevDebug)
                                            {
                                                // 测试环境
                                            }
                                            else
                                            {
                                                // 正式环境，直接发送关门指令
                                            }

                                            //检测门是否关闭
                                            _strMsg = $"{from.ChamberName}->{to.ChamberName}【PUT  Slot:{toSlotId}】，发送CHA关门指令后，检测门关闭状态超时,请确认后重试！";
                                            bool isDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.CHADoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);
                                            if (!isDoorClosed)
                                            {
                                                _strMsg = $"{to.ChamberName}门未关闭，无法执行下去,已退出程序";
                                                UILogService.AddLog(_strMsg);
                                                MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                                return false;
                                            }
                                            _strMsg = $"已检测门已关闭 {to.ChamberName}【PUT  Slot:{toSlotId}】";
                                            break;

                                        case EnuChamberName.CHB:
                                            if (Golbal.IsDevDebug)
                                            {
                                                // 测试环境
                                            }
                                            else
                                            {
                                                // 正式环境，直接发送关门指令
                                            }

                                            //检测门是否关闭
                                            _strMsg = $"{from.ChamberName}->{to.ChamberName}【PUT  Slot:{toSlotId}】，发送CHB关门指令后，检测门关闭状态超时,请确认后重试！";
                                            isDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.CHBDoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);

                                            if (!isDoorClosed)
                                            {
                                                _strMsg = $"{to.ChamberName}门未关闭，无法执行下去,已退出程序";
                                                UILogService.AddLog(_strMsg);
                                                MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                                return false;
                                            }
                                            _strMsg = $"已检测门已关闭 {to.ChamberName}【PUT  Slot:{toSlotId}】";
                                            break;

                                        case EnuChamberName.CHC:
                                            if (Golbal.IsDevDebug)
                                            {
                                                // 测试环境
                                            }
                                            else
                                            {
                                                // 正式环境，直接发送关门指令
                                            }

                                            //检测门是否关闭
                                            _strMsg = $"{from.ChamberName}->{to.ChamberName}【PUT  Slot:{toSlotId}】，发送CHC关门指令后，检测门关闭状态超时,请确认后重试！";
                                            isDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.CHCDoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);

                                            if (!isDoorClosed)
                                            {
                                                _strMsg = $"{to.ChamberName}门未关闭，无法执行下去,已退出程序";
                                                UILogService.AddLog(_strMsg);
                                                MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                                return false;
                                            }
                                            _strMsg = $"已检测门已关闭 {to.ChamberName} 【PUT】";
                                            break;

                                        case EnuChamberName.Cooling:
                                            break;

                                        case EnuChamberName.Cassette:
                                            // 判断是否还有未处理的Wafer，后面待取不关门，否则关门
                                            if (!iR400ViewModel.Cassette.HasUnfinishedWafer())
                                            {
                                                if (Golbal.IsDevDebug)
                                                {
                                                    // 测试环境
                                                }
                                                else
                                                {
                                                    // 正式环境，直接发送关门指令
                                                }
                                                _strMsg = $"已发送关门指令： {to.ChamberName} 【PUT  Slot:{toSlotId}】";
                                                UILogService.AddLog(_strMsg);

                                                //检测门是否关闭
                                                _strMsg = $"{from.ChamberName}->{to.ChamberName}【PUT  Slot:{toSlotId}】，发送LoadLock关门指令后，检测门关闭状态超时,请确认后重试！";
                                                isDoorClosed = await CheckDoorClosedAsync(SlitDoorPLCCmdConstants.LoadLockDoorCloseState, TimeSpan.FromSeconds(SlitDoorPLCCmdConstants.DoorCloseCheckTimeout), _strMsg, "检测门关闭状态超时，请确认", Golbal.IsDevDebug ? Visibility.Visible : Visibility.Collapsed);

                                                if (!isDoorClosed)
                                                {
                                                    _strMsg = $"{to.ChamberName}门未关闭，无法执行下去,已退出程序";
                                                    UILogService.AddLog(_strMsg);
                                                    MessageBox.Show(_strMsg, "门未关闭，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                                    return false;
                                                }
                                                _strMsg = $"已检测门已关闭 {to.ChamberName} 【PUT】";
                                            }
                                            break;

                                        case EnuChamberName.LoadLock:
                                            break;

                                        case EnuChamberName.RobotArmNose:
                                            break;

                                        case EnuChamberName.RobotArmSmooth:
                                            break;

                                        case EnuChamberName.Home:
                                            break;

                                        case EnuChamberName.Host:
                                            break;

                                        case EnuChamberName.Buffer:
                                            break;
                                    }
                                    if (!string.IsNullOrEmpty(_strMsg))
                                    {
                                        UILogService.AddLog(_strMsg);
                                    }
                                }
                                else
                                {
                                    MessageBox.Show(_strMsg, "Put指令执行失败，将被终止，无法执行下去", MessageBoxButton.OK, MessageBoxImage.Error);
                                }

                                // 如果目标是冷却腔体，那么等待冷却完成后再返回
                                if (to is CoolingChamber coolChamber)
                                {
                                    // 冷却腔体处理逻辑已移除
                                }
                                else
                                {
                                    Console.WriteLine();
                                }
                            }
                        }
                    }
                    else
                    {
                        UILogService.AddLog(_strMsg);
                        Console.WriteLine(_strMsg);
                    }
                }
                else
                {
                    _strMsg = $"{this.ChamberName}机械臂【{enuArmFetchSide}端】抓取已满了，无法继续";
                    UILogService.AddLog(_strMsg);
                    Console.WriteLine(_strMsg);
                }
            }
            else
            {
                _strMsg = $"异常信息：源Wafer不能为空、目的Wafer数量不能满\r\nfrom：{from.ToString()}\r\nTo：{to.ToString()}";//ToDO：这里需要修改，有异常
                UILogService.AddLog(_strMsg);
            }

            return blResult;
        }

        /// <summary>
        /// 在指定的时间内检测门是否关闭
        /// </summary>
        /// <param name="doorStateAddress"></param>
        /// <param name="timeout"></param>
        /// <returns></returns>
        public async Task<bool> CheckDoorClosedAsync(string doorStateAddress, TimeSpan timeout, string message, string title, Visibility visibility = Visibility.Collapsed)
        {
            return true;
            var startTime = DateTime.Now;

            while (true)
            {
                // 根据传入的地址检测门是否关闭
                //bool isDoorClose = iR400ViewModel._adsHelper.Read<bool>(doorStateAddress);
                bool isDoorClose = true;
                if (isDoorClose)
                {
                    return true; // 门已关闭
                }

                // 检查是否超时
                if (DateTime.Now - startTime > timeout)
                {
                    // 超时，显示自定义对话框
                    var customMessageBoxResult = await Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        var customMessageBox = new CustomMessageBox(message, title, visibility);
                        return customMessageBox.ShowDialog() == true ? customMessageBox.Result : CustomMessageBox.CustomMessageBoxResult.Exit;
                    });

                    switch (customMessageBoxResult)
                    {
                        case CustomMessageBox.CustomMessageBoxResult.Retry:
                            // 调整在5秒内重试
                            startTime = DateTime.Now.Subtract(timeout).AddSeconds(5);
                            break;

                        case CustomMessageBox.CustomMessageBoxResult.Exit:
                            // 退出
                            return false;

                        case CustomMessageBox.CustomMessageBoxResult.Continue:
                            // 忽略
                            return true;
                    }
                }

                await Task.Delay(500); // 等待一段时间后再次检测
            }
        }
    }
}