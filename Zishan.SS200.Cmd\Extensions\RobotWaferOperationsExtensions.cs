using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using Zishan.SS200.Cmd.Common;
using Zishan.SS200.Cmd.Enums;
using Zishan.SS200.Cmd.Enums.McuCmdIndex;
using Zishan.SS200.Cmd.Enums.Command;
using Zishan.SS200.Cmd.Services.Interfaces;
using Zishan.SS200.Cmd.Utilities;
using Zishan.SS200.Cmd.Constants.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Config.SS200.SubsystemConfigure.Robot;
using Zishan.SS200.Cmd.Config;
using Zishan.SS200.Cmd.Constants;
using Zishan.SS200.Cmd.Enums.Basic;
using Zishan.SS200.Cmd.Models.SS200;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Chamber;
using Zishan.SS200.Cmd.Models.SS200.SubSystemStatus.Robot;
using Zishan.SS200.Cmd.Services;

namespace Zishan.SS200.Cmd.Extensions
{
    /// <summary>
    /// 机器人晶圆搬运操作扩展方法  方法参数后面添加 CancellationToken cancellationToken，支持取消操作
    /// </summary>
    public static class RobotWaferOperationsExtensions
    {
        private static string _msg = string.Empty;

        // 缓存 InterLock 实例，减少重复访问
        private static readonly SS200InterLockMain _interLock = SS200InterLockMain.Instance;

        #region 一级命令 - 晶圆传输流程

        /// <summary>
        /// 执行完整的晶圆传输流程（从源端到目标端，通过Z轴上下移动完成取放片操作）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型（Nose端或Smooth端）</param>
        /// <param name="sourceStationType">源站点类型</param>
        /// <param name="targetStationType">目标站点类型</param>
        /// <param name="sourceSlotNumber">源Slot号（对于晶圆盒）</param>
        /// <param name="targetSlotNumber">目标Slot号（对于晶圆盒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> TrasferWaferAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType sourceStationType,
            EnuLocationStationType targetStationType,
            int sourceSlotNumber,
            int targetSlotNumber,
            bool isTRZAxisReturnZeroed,
            CancellationToken cancellationToken)
        {
            // 🔥 整体晶圆传输性能监控开始
            var transferStopwatch = new StopwatchHelper($"晶圆传输总流程-{endType}端-{sourceStationType}(Slot:{sourceSlotNumber})→{targetStationType}(Slot:{targetSlotNumber})");
            transferStopwatch.Start();

            // 使用层次化日志记录整个传输流程
            UILogService.AddLogAndIncreaseIndent($"开始执行{endType}端 晶圆传输：{sourceStationType}(Slot: {sourceSlotNumber}) → {targetStationType}(Slot: {targetSlotNumber})");

            try
            {
                if (isTRZAxisReturnZeroed)
                {
                    // 1. 机器人初始化（三轴回零）
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("第一步：机器人初始化（三轴回零）");

                        // 🔥 机器人初始化性能监控
                        var initStopwatch = new StopwatchHelper($"机器人初始化(三轴回零)-{endType}端");
                        initStopwatch.Start();

                        var initResult = await InitializeRobotAsync(cmdService, cancellationToken);

                        initStopwatch.Stop(warnThresholdMs: 15000);

                        if (!initResult.Success)
                        {
                            UILogService.AddErrorLog($"机器人初始化失败: {initResult.Message}");
                            UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输失败: {initResult.Message}");
                            transferStopwatch.Stop(warnThresholdMs: 30000);
                            return (false, $"TrasferWafer失败: {initResult.Message}");
                        }

                        UILogService.AddSuccessLog("机器人初始化成功");
                    }
                }

                // 2. 执行GetWafer操作
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"第二步：从{sourceStationType}(Slot: {sourceSlotNumber})获取晶圆");

                    // 🔥 GetWafer 操作性能监控
                    var getWaferStopwatch = new StopwatchHelper($"GetWafer操作-{endType}端-{sourceStationType}(Slot:{sourceSlotNumber})");
                    getWaferStopwatch.Start();

                    var getResult = await GetWaferAsync(cmdService, endType, sourceStationType, sourceSlotNumber, cancellationToken);

                    getWaferStopwatch.Stop(warnThresholdMs: 12000);

                    if (!getResult.Success)
                    {
                        UILogService.AddErrorLog($"获取晶圆失败: {getResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输失败: {getResult.Message}");
                        transferStopwatch.Stop(warnThresholdMs: 30000);
                        return (false, $"TrasferWafer失败: {getResult.Message}");
                    }
                    UILogService.AddSuccessLog($"从{sourceStationType}(Slot: {sourceSlotNumber})获取晶圆成功");
                }

                // 3. 执行PutWafer操作
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"第三步：将晶圆放置到{targetStationType}(Slot: {targetSlotNumber})");

                    // 🔥 PutWafer 操作性能监控
                    var putWaferStopwatch = new StopwatchHelper($"PutWafer操作-{endType}端-{targetStationType}(Slot:{targetSlotNumber})");
                    putWaferStopwatch.Start();

                    var putResult = await PutWaferAsync(cmdService, endType, targetStationType, targetSlotNumber, cancellationToken);

                    putWaferStopwatch.Stop(warnThresholdMs: 12000);

                    if (!putResult.Success)
                    {
                        UILogService.AddErrorLog($"放置晶圆失败: {putResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输失败: {putResult.Message}");
                        transferStopwatch.Stop(warnThresholdMs: 30000);
                        return (false, $"TrasferWafer失败: {putResult.Message}");
                    }
                    UILogService.AddSuccessLog($"将晶圆放置到{targetStationType}(Slot: {targetSlotNumber})成功");
                }

                // 🔥 整体晶圆传输性能监控结束 - 成功
                transferStopwatch.Stop(warnThresholdMs: 30000);

                UILogService.DecreaseIndentAndAddSuccessLog($"✅ 晶圆传输成功：{sourceStationType}(Slot: {sourceSlotNumber}) → {targetStationType}(Slot: {targetSlotNumber})");
                return (true, $"成功完成从{sourceStationType}(Slot: {sourceSlotNumber})到{targetStationType}(Slot: {targetSlotNumber})的晶圆传输");
            }
            catch (Exception ex)
            {
                // 🔥 整体晶圆传输性能监控结束 - 异常
                transferStopwatch.Stop(warnThresholdMs: 30000);

                UILogService.DecreaseIndentAndAddErrorLog($"晶圆传输操作异常: {ex.Message}");
                return (false, $"TrasferWafer操作异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 执行Pin Search测试流程
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型（Nose端或Smooth端）</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value)> PinSearchAsync(this IS200McuCmdService cmdService, EnuRobotEndType endType, bool isTRZAxisReturnZeroed = false)
        {
            return await PinSearchAsync(cmdService, endType, isTRZAxisReturnZeroed, CancellationToken.None);
        }

        /// <summary>
        /// 执行Pin Search测试流程（支持取消令牌）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型（Nose端或Smooth端）</param>
        /// <param name="isTRZAxisReturnZeroed">是否TRZ轴归零</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message, int PinSearchP1Value, int PinSearchP2Value)> PinSearchAsync(this IS200McuCmdService cmdService, EnuRobotEndType endType, bool isTRZAxisReturnZeroed, CancellationToken cancellationToken)
        {
            // 使用层次化日志记录Pin Search测试流程
            UILogService.AddLogAndIncreaseIndent($"开始执行{endType}端 Pin Search测试");

            try
            {
                if (isTRZAxisReturnZeroed)
                {
                    // 1. 机器人初始化（三轴回零）
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("执行三轴回零");
                        var initResult = await InitializeRobotAsync(cmdService, cancellationToken);
                        if (!initResult.Success)
                        {
                            UILogService.AddErrorLog($"三轴回零失败: {initResult.Message}");
                            UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {initResult.Message}");
                            return (false, $"三轴回零失败: {initResult.Message}", -1, -1);
                        }
                        UILogService.AddSuccessLog("三轴回零成功");
                    }
                }

                // 2. T轴旋转到Cassette位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("T轴旋转到Cassette位置");
                    var rotateResult = await MoveTAxisToLocationAsync(cmdService, endType, EnuLocationStationType.Cassette, cancellationToken);
                    if (!rotateResult.Success)
                    {
                        UILogService.AddErrorLog($"T轴旋转到Cassette位置失败: {rotateResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {rotateResult.Message}");
                        return (false, $"T轴旋转到Cassette位置失败: {rotateResult.Message}", -1, -1);
                    }
                    UILogService.AddSuccessLog("T轴旋转到Cassette位置成功");
                }

                // 3. 移动Z轴到PinSearch高度
                using (UILogService.CreateIndentScope())
                {
                    var pinSearchHeight = RobotPositionParameters.GetZAxisPinSearchPosition();
                    UILogService.AddLog($"获取Z轴Pin Search初始高度: {pinSearchHeight}");

                    UILogService.AddLog("移动Z轴到PinSearch高度");
                    var zAxisResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeight);
                    if (zAxisResult.ReturnInfo != 0)
                    {
                        UILogService.AddErrorLog($"移动Z轴到PinSearch高度失败: {zAxisResult.ReturnInfo}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {zAxisResult.ReturnInfo}");
                        return (false, $"移动Z轴到PinSearch高度失败: {zAxisResult.ReturnInfo}", -1, -1);
                    }
                    UILogService.AddSuccessLog("移动Z轴到PinSearch高度成功");
                }

                // 4. R轴伸展到Cassette位置【安全起见，这里先注释掉】
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddWarningLog("Todo：R轴伸展到Cassette位置【安全起见，这里先注释掉】");

                    UILogService.AddLog($"伸展R轴到Cassette位置...");
                    var rAxisResult = await cmdService.ExecuteRobotRAxisCommandAsync(
                        RobotPositionParameters.GetRAxisPosition(endType, EnuLocationStationType.Cassette));
                    if (rAxisResult.ReturnInfo != 0)
                    {
                        UILogService.AddErrorLog($"伸展R轴到Cassette位置失败: {rAxisResult.ReturnInfo}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {rAxisResult.ReturnInfo}");
                        return (false, $"伸展R轴到Cassette位置失败: {rAxisResult.ReturnInfo}", -1, -1);
                    }
                    UILogService.AddSuccessLog("伸展R轴到Cassette位置成功");
                }

                // 5. 执行PinSearch命令
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLogAndIncreaseIndent($"执行{endType}端 PinSearch命令");

                    int lowestStepForPinSearch = RobotConfigureSettings.GetPinSearchLowestStep();
                    UILogService.AddLog($"PinSearch最低步进: {lowestStepForPinSearch}");

                    var cmdResult = await S200McuCmdServiceExtensions.ExecuteDeviceCommandAsync(cmdService,
                        EnuMcuDeviceType.Robot,
                        EnuRobotCmdIndex.PinSearch.ToString(),
                        new List<ushort>
                        {
                            (ushort)(lowestStepForPinSearch >> 16),      // Prepressing Position H
                            (ushort)(lowestStepForPinSearch & 0xFFFF),   // Prepressing Position L
                            100,    // Pin Search Speed
                            8000,   // Start slope
                            8000,   // Stop slope
                            500     // Run Current
                        },
                        cancellationToken); // 支持取消令牌

                    //记录Robot IO口状态到日志
                    using (UILogService.CreateIndentScope())
                    {
                        UILogService.AddLog("记录Robot IO口状态到日志:");

                        // 记录Robot输入线圈状态 - 使用InterLock访问
                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog("Robot数字输入(DI)状态:");

                            using (UILogService.CreateIndentScope())
                            {
                                // 记录关键的Robot DI状态 - 通过InterLock访问
                                var rdi1 = _interLock.IOInterface.Robot.RDI1_PaddleSensor1Left;
                                string rdi1Status = rdi1.Value ? "ON(1)" : "OFF(0)";
                                UILogService.AddLog($"RDI1_PaddleSensor1Left(Paddle传感器1左侧): {rdi1Status} - {rdi1.Content}");

                                var rdi2 = _interLock.IOInterface.Robot.RDI2_PaddleSensor2Right;
                                string rdi2Status = rdi2.Value ? "ON(1)" : "OFF(0)";
                                UILogService.AddLog($"RDI2_PaddleSensor2Right(Paddle传感器2右侧): {rdi2Status} - {rdi2.Content}");

                                var rdi3 = _interLock.IOInterface.Robot.RDI3_PinSearch1;
                                string rdi3Status = rdi3.Value ? "ON(1)" : "OFF(0)";
                                UILogService.AddLog($"RDI3_PinSearch1(Pin搜索传感器1): {rdi3Status} - {rdi3.Content}");

                                var rdi4 = _interLock.IOInterface.Robot.RDI4_PinSearch2;
                                string rdi4Status = rdi4.Value ? "ON(1)" : "OFF(0)";
                                UILogService.AddLog($"RDI4_PinSearch2(Pin搜索传感器2): {rdi4Status} - {rdi4.Content}");
                            }
                        }

                        UILogService.AddSuccessLog("Robot IO口状态记录完成");
                    }

                    if (cmdResult.ReturnInfo == 0)
                    {
                        UILogService.AddLog("PinSearch命令执行成功");

                        // 主动触发寄存器更新以获取最新的Pin Search结果值
                        UILogService.AddLog("触发寄存器更新以获取最新Pin Search值...");
                        await cmdService.RefreshAlarmRegistersAsync();
                        UILogService.AddLog("寄存器更新完成");

                        // 获取 Pin Search结果值，并且保存
                        // 调试信息：检查寄存器数量和地址映射
                        UILogService.AddLog($"RobotAlarmRegisters总数: {cmdService.RobotAlarmRegisters.Count}");

                        // 显示索引9和11的寄存器详细信息
                        if (cmdService.RobotAlarmRegisters.Count > 9)
                        {
                            var reg9 = cmdService.RobotAlarmRegisters[9];
                            UILogService.AddLog($"索引9寄存器 - 地址: 0x{reg9.Address:X}, 值: 0x{reg9.Value:X}, 合并值: {reg9.Combinevalue}, 类型: {reg9.CombinevalueValueType}");
                        }

                        if (cmdService.RobotAlarmRegisters.Count > 11)
                        {
                            var reg11 = cmdService.RobotAlarmRegisters[11];
                            UILogService.AddLog($"索引11寄存器 - 地址: 0x{reg11.Address:X}, 值: 0x{reg11.Value:X}, 合并值: {reg11.Combinevalue}, 类型: {reg11.CombinevalueValueType}");
                        }

                        // 查找实际的Pin Search寄存器（地址0x109和0x10B）
                        var pinSearchP1Reg = cmdService.RobotAlarmRegisters.FirstOrDefault(r => r.Address == 0x109);
                        var pinSearchP2Reg = cmdService.RobotAlarmRegisters.FirstOrDefault(r => r.Address == 0x10B);

                        if (pinSearchP1Reg != null)
                        {
                            UILogService.AddLog($"Pin Search P1寄存器(0x109) - 值: 0x{pinSearchP1Reg.Value:X}, 合并值: {pinSearchP1Reg.Combinevalue}");
                        }
                        else
                        {
                            UILogService.AddErrorLog("未找到Pin Search P1寄存器(地址0x109)");
                        }

                        if (pinSearchP2Reg != null)
                        {
                            UILogService.AddLog($"Pin Search P2寄存器(0x10B) - 值: 0x{pinSearchP2Reg.Value:X}, 合并值: {pinSearchP2Reg.Combinevalue}");
                        }
                        else
                        {
                            UILogService.AddErrorLog("未找到Pin Search P2寄存器(地址0x10B)");
                        }

                        // 使用正确的方式获取Pin Search值   修复获取到的值不是最新的Pin Search值，而是之前可能的值，读取之前最好触发UpdateAlarmRegistersAsync()读一下再获取
                        int pinSearchP1Value = pinSearchP1Reg?.Combinevalue ?? (cmdService.RobotAlarmRegisters.Count > 9 ? cmdService.RobotAlarmRegisters[9].Combinevalue : 0);
                        int pinSearchP2Value = pinSearchP2Reg?.Combinevalue ?? (cmdService.RobotAlarmRegisters.Count > 11 ? cmdService.RobotAlarmRegisters[11].Combinevalue : 0);

                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog($"Pin Search结果值: P1={pinSearchP1Value}, P2={pinSearchP2Value}");

                            // 🔥 修复：在UI线程上更新基准值，确保UI立即响应
                            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                            {
                                // 记录Pin Serarch两边结果值，取平均值
                                switch (endType)
                                {
                                    case EnuRobotEndType.Smooth:
                                        cmdService.SmoothBasePinSearchValue = (pinSearchP1Value + pinSearchP2Value) / 2;
                                        UILogService.AddLog($"保存Smooth端基准值: {cmdService.SmoothBasePinSearchValue}");
                                        break;

                                    case EnuRobotEndType.Nose:
                                        cmdService.NoseBasePinSearchValue = (pinSearchP1Value + pinSearchP2Value) / 2;
                                        UILogService.AddLog($"保存Nose端基准值: {cmdService.NoseBasePinSearchValue}");
                                        break;
                                }
                            });
                        }

                        UILogService.AddLog($"{endType}端 PinSearch命令执行成功");

                        // 重新移动Z轴到PinSearch高度
                        using (UILogService.CreateIndentScope())
                        {
                            var pinSearchHeight = RobotPositionParameters.GetZAxisPinSearchPosition();
                            UILogService.AddLog($"重新获取Z轴Pin Search初始高度: {pinSearchHeight}");

                            UILogService.AddLog("重新移动Z轴到PinSearch高度");
                            var zAxisResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeight);
                            if (zAxisResult.ReturnInfo != 0)
                            {
                                UILogService.AddErrorLog($"重新移动Z轴到PinSearch高度失败: {zAxisResult.ReturnInfo}");
                                UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {zAxisResult.ReturnInfo}");
                                return (false, $"重新移动Z轴到PinSearch高度失败: {zAxisResult.ReturnInfo}", -1, -1);
                            }
                            UILogService.AddSuccessLog("重新移动Z轴到PinSearch高度成功");
                        }
                        // R轴回原点
                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLog($"{endType}端 R轴开始回原点");
                            var rAxisResult = await ZeroRAxisAsync(cmdService, cancellationToken);
                            if (!rAxisResult.Success)
                            {
                                UILogService.AddErrorLog($"R轴回原点失败: {rAxisResult.Message}");
                                UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {rAxisResult.Message}");
                                return (false, $"R轴回原点失败: {rAxisResult.Message}", pinSearchP1Value, pinSearchP2Value);
                            }
                            UILogService.AddSuccessLog($"{endType}端 R轴回原点成功");
                        }

                        UILogService.DecreaseIndentAndAddSuccessLog($"成功完成{endType}端 Pin Search测试");
                        return (true, $"成功完成{endType}端 Pin Search测试", pinSearchP1Value, pinSearchP2Value);
                    }
                    else
                    {
                        // PinSearch命令执行失败，自动重试一次
                        UILogService.AddWarningLog($"{endType}端 PinSearch命令执行失败，返回值: 0x{cmdResult.ReturnInfo:X4}, 响应: {cmdResult.Response}");
                        UILogService.AddLog($"自动重试：重新移动Z轴到PinSearch高度，完整执行一次PinSearch流程");

                        // 第一步：重新移动Z轴到PinSearch高度
                        using (UILogService.CreateIndentScope())
                        {
                            var pinSearchHeight = RobotPositionParameters.GetZAxisPinSearchPosition();
                            UILogService.AddLog($"重新获取Z轴Pin Search初始高度: {pinSearchHeight}");

                            UILogService.AddLog("重新移动Z轴到PinSearch高度");
                            var zAxisResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeight);
                            if (zAxisResult.ReturnInfo != 0)
                            {
                                UILogService.AddErrorLog($"重新移动Z轴到PinSearch高度失败: {zAxisResult.ReturnInfo}");
                                UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {zAxisResult.ReturnInfo}");
                                return (false, $"重新移动Z轴到PinSearch高度失败: {zAxisResult.ReturnInfo}", -1, -1);
                            }
                            UILogService.AddSuccessLog("重新移动Z轴到PinSearch高度成功");
                        }

                        // 第二步：再次执行PinSearch命令
                        using (UILogService.CreateIndentScope())
                        {
                            UILogService.AddLogAndIncreaseIndent($"重新执行{endType}端 PinSearch命令");

                            int retryLowestStepForPinSearch = RobotConfigureSettings.GetPinSearchLowestStep();
                            UILogService.AddLog($"PinSearch最低步进: {retryLowestStepForPinSearch}");

                            var retryCmdResult = await S200McuCmdServiceExtensions.ExecuteDeviceCommandAsync(cmdService,
                                EnuMcuDeviceType.Robot,
                                EnuRobotCmdIndex.PinSearch.ToString(),
                                new List<ushort>
                                {
                                    (ushort)(retryLowestStepForPinSearch >> 16),      // Prepressing Position H
                                    (ushort)(retryLowestStepForPinSearch & 0xFFFF),   // Prepressing Position L
                                    100,    // Pin Search Speed
                                    8000,   // Start slope
                                    8000,   // Stop slope
                                    500     // Run Current
                                },
                                cancellationToken);

                            if (retryCmdResult.ReturnInfo == 0)
                            {
                                UILogService.AddLog("重新执行PinSearch命令成功");

                                // 触发寄存器更新以获取最新的Pin Search结果值
                                UILogService.AddLog("触发寄存器更新以获取最新Pin Search值...");
                                await cmdService.RefreshAlarmRegistersAsync();
                                UILogService.AddLog("寄存器更新完成");

                                // 获取Pin Search结果值
                                var pinSearchP1Reg = cmdService.RobotAlarmRegisters.FirstOrDefault(r => r.Address == 0x109);
                                var pinSearchP2Reg = cmdService.RobotAlarmRegisters.FirstOrDefault(r => r.Address == 0x10B);

                                int pinSearchP1Value = pinSearchP1Reg?.Combinevalue ?? (cmdService.RobotAlarmRegisters.Count > 9 ? cmdService.RobotAlarmRegisters[9].Combinevalue : 0);
                                int pinSearchP2Value = pinSearchP2Reg?.Combinevalue ?? (cmdService.RobotAlarmRegisters.Count > 11 ? cmdService.RobotAlarmRegisters[11].Combinevalue : 0);

                                using (UILogService.CreateIndentScope())
                                {
                                    UILogService.AddLog($"Pin Search结果值: P1={pinSearchP1Value}, P2={pinSearchP2Value}");

                                    // 🔥 修复：在UI线程上更新基准值，确保UI立即响应
                                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                                    {
                                        // 记录Pin Search两边结果值，取平均值
                                        switch (endType)
                                        {
                                            case EnuRobotEndType.Smooth:
                                                cmdService.SmoothBasePinSearchValue = (pinSearchP1Value + pinSearchP2Value) / 2;
                                                UILogService.AddLog($"保存Smooth端基准值: {cmdService.SmoothBasePinSearchValue}");
                                                break;

                                            case EnuRobotEndType.Nose:
                                                cmdService.NoseBasePinSearchValue = (pinSearchP1Value + pinSearchP2Value) / 2;
                                                UILogService.AddLog($"保存Nose端基准值: {cmdService.NoseBasePinSearchValue}");
                                                break;
                                        }
                                    });
                                }

                                UILogService.AddLog($"{endType}端 PinSearch命令重新执行成功");

                                // 第三步：执行成功后的完整处理流程 - 重新移动Z轴到PinSearch高度
                                using (UILogService.CreateIndentScope())
                                {
                                    var pinSearchHeightFinal = RobotPositionParameters.GetZAxisPinSearchPosition();
                                    UILogService.AddLog($"最终获取Z轴Pin Search初始高度: {pinSearchHeightFinal}");

                                    UILogService.AddLog("最终移动Z轴到PinSearch高度");
                                    var zAxisFinalResult = await cmdService.ExecuteRobotZAxisCommandAsync(pinSearchHeightFinal);
                                    if (zAxisFinalResult.ReturnInfo != 0)
                                    {
                                        UILogService.AddErrorLog($"最终移动Z轴到PinSearch高度失败: {zAxisFinalResult.ReturnInfo}");
                                        UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {zAxisFinalResult.ReturnInfo}");
                                        return (false, $"最终移动Z轴到PinSearch高度失败: {zAxisFinalResult.ReturnInfo}", -1, -1);
                                    }
                                    UILogService.AddSuccessLog("最终移动Z轴到PinSearch高度成功");
                                }

                                // 第四步：R轴回原点
                                using (UILogService.CreateIndentScope())
                                {
                                    UILogService.AddLog($"{endType}端 R轴开始回原点");
                                    var rAxisResult = await ZeroRAxisAsync(cmdService, cancellationToken);
                                    if (!rAxisResult.Success)
                                    {
                                        UILogService.AddErrorLog($"R轴回原点失败: {rAxisResult.Message}");
                                        UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败: {rAxisResult.Message}");
                                        return (false, $"R轴回原点失败: {rAxisResult.Message}", pinSearchP1Value, pinSearchP2Value);
                                    }
                                    UILogService.AddSuccessLog($"{endType}端 R轴回原点成功");
                                }

                                UILogService.DecreaseIndentAndAddSuccessLog($"成功完成{endType}端 Pin Search测试（自动重试后成功）");
                                return (true, $"成功完成{endType}端 Pin Search测试（自动重试后成功）", pinSearchP1Value, pinSearchP2Value);
                            }
                            else
                            {
                                UILogService.AddErrorLog($"重新执行{endType}端 PinSearch命令仍然失败，返回值: 0x{retryCmdResult.ReturnInfo:X4}");
                                UILogService.DecreaseIndentAndAddErrorLog($"Pin Search测试失败");
                                return (false, $"{endType}端PinSearch自动重试后仍然失败，返回值: 0x{retryCmdResult.ReturnInfo:X4}, 响应: {retryCmdResult.Response}", -1, -1);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"{endType}端 Pin Search测试异常: {ex.Message}");
                return (false, $"{endType}端 Pin Search测试异常: {ex.Message}", -1, -1);
            }
        }

        #endregion 一级命令 - 晶圆传输流程

        #region 二级命令 - 晶圆取放操作

        #region 取晶圆操作

        /// <summary>
        /// 从指定位置获取晶圆
        /// 支持工艺腔体（顶针升降模式）和晶圆盒/冷却腔（Z轴升降模式）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型（Nose端或Smooth端）</param>
        /// <param name="sourceStationType">源站点类型</param>
        /// <param name="slotNumber">Slot号（对于晶圆盒和冷却腔）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> GetWaferAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType sourceStationType,
            int slotNumber,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录获取晶圆流程
            UILogService.AddLogAndIncreaseIndent($"开始从{sourceStationType}获取晶圆 (端口: {endType}, Slot: {slotNumber})");

            try
            {
                // 执行InterLock安全检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行InterLock安全检查");
                    var interlockResult = await CheckInterLockAsync(cmdService, sourceStationType, cancellationToken);
                    if (!interlockResult.Success)
                    {
                        UILogService.AddErrorLog($"InterLock检查失败: {interlockResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {interlockResult.Message}");
                        return (false, $"GetWafer失败: {interlockResult.Message}");
                    }
                    UILogService.AddSuccessLog("InterLock安全检查通过");
                }

                // 确认源位置有晶圆
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"确认{sourceStationType}位置有晶圆");
                    var waferCheckResult = await CheckWaferPresenceAsync(cmdService, sourceStationType, slotNumber, true, cancellationToken);
                    if (!waferCheckResult.Success)
                    {
                        UILogService.AddErrorLog($"源位置晶圆检查失败: {waferCheckResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {waferCheckResult.Message}");
                        return (false, $"GetWafer失败: {waferCheckResult.Message}");
                    }
                    UILogService.AddSuccessLog($"{sourceStationType}位置确认有晶圆");
                }

                // 确认机械臂上无晶圆
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("确认Robot机械臂上无晶圆");
                    var armWaferCheckResult = await CheckRobotArmWaferAsync(cmdService, endType, false, cancellationToken);
                    if (!armWaferCheckResult.Success)
                    {
                        UILogService.AddErrorLog($"Robot机械臂晶圆检查失败: {armWaferCheckResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {armWaferCheckResult.Message}");
                        return (false, $"GetWafer失败: {armWaferCheckResult.Message}");
                    }
                    UILogService.AddSuccessLog("Robot机械臂确认无晶圆");
                }

                // 1. 移动T轴到源站点位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动T轴到{sourceStationType}位置");
                    var tAxisResult = await MoveTAxisToLocationAsync(cmdService, endType, sourceStationType, cancellationToken);
                    if (!tAxisResult.Success)
                    {
                        UILogService.AddErrorLog($"T轴移动失败: {tAxisResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {tAxisResult.Message}");
                        return (false, $"GetWafer失败: {tAxisResult.Message}");
                    }
                    UILogService.AddSuccessLog($"T轴移动到{sourceStationType}位置成功");
                }

                // 2. 移动Z轴到取片位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动Z轴到{sourceStationType}取片位置");
                    var zAxisResult = await MoveZAxisToGetPositionAsync(cmdService, endType, sourceStationType, slotNumber, cancellationToken);
                    if (!zAxisResult.Success)
                    {
                        UILogService.AddErrorLog($"Z轴移动到取片位置失败: {zAxisResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {zAxisResult.Message}");
                        return (false, $"GetWafer失败: {zAxisResult.Message}");
                    }
                    UILogService.AddSuccessLog($"Z轴移动到{sourceStationType}取片位置成功");

                    switch (sourceStationType)
                    {
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            UILogService.AddLog($"打开{sourceStationType} SlitDoor，顶针降下去待取片");
                            break;

                        case EnuLocationStationType.Cassette:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            break;
                    }
                }

                // 3. 伸展R轴到源站点位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"伸展R轴到{sourceStationType}位置");
                    var rAxisResult = await MoveRAxisToLocationAsync(cmdService, endType, sourceStationType, cancellationToken);
                    if (!rAxisResult.Success)
                    {
                        UILogService.AddErrorLog($"R轴伸展失败: {rAxisResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {rAxisResult.Message}");
                        return (false, $"GetWafer失败: {rAxisResult.Message}");
                    }
                    UILogService.AddSuccessLog($"R轴伸展到{sourceStationType}位置成功");
                }

                // 4. 根据位置类型执行不同的取片操作
                using (UILogService.CreateIndentScope())
                {
                    switch (sourceStationType)
                    {
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            // 工艺腔体模式：顶针升降取片
                            UILogService.AddLogAndIncreaseIndent($"执行{sourceStationType}顶针升降取片操作");

                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog($"等待{sourceStationType}顶针上升，将晶圆顶起到取片高度");
                                await Task.Delay(100, cancellationToken); // 等待顶针上升
                                UILogService.AddLog("顶针上升完成");
                            }

                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog($"等待{sourceStationType}顶针下降，将晶圆放置到Robot机械臂上");
                                await Task.Delay(100, cancellationToken); // 等待顶针下降
                                UILogService.AddLog("顶针下降完成");
                            }

                            UILogService.DecreaseIndentAndAddSuccessLog($"{sourceStationType}顶针升降取片操作完成");
                            break;

                        case EnuLocationStationType.Cassette:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            // 晶圆盒/冷却腔模式：Z轴升降取片
                            UILogService.AddLogAndIncreaseIndent($"执行{sourceStationType}Z轴升降取片操作");

                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog($"Z轴上升到{sourceStationType}放片位置，从下方托起晶圆");
                                var zAxisResult = await MoveZAxisToPutPositionAsync(cmdService, endType, sourceStationType, slotNumber, cancellationToken);
                                if (!zAxisResult.Success)
                                {
                                    UILogService.AddErrorLog($"Z轴上升到放片位置失败: {zAxisResult.Message}");
                                    UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {zAxisResult.Message}");
                                    UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {zAxisResult.Message}");
                                    return (false, $"GetWafer失败: {zAxisResult.Message}");
                                }
                                UILogService.AddSuccessLog($"Z轴上升到{sourceStationType}放片位置成功，晶圆已托起");
                            }

                            UILogService.DecreaseIndentAndAddSuccessLog($"{sourceStationType}Z轴升降取片操作完成");
                            break;

                        default:
                            UILogService.AddErrorLog($"不支持的源站点类型: {sourceStationType}");
                            UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: 不支持的源站点类型 {sourceStationType}");
                            return (false, $"GetWafer失败: 不支持的源站点类型 {sourceStationType}");
                    }
                }

                // 5. 执行状态交换（源站点到挡板）
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"Todo：需要执行Wafer状态交换: {sourceStationType}到{endType}");
                    /*
                    //执行Wafer状态交换，后面带优化
                    var statusResult = await ExecuteStatusExchangeAsync(cmdService, endType, sourceStationType, EnuLocationStationType.Cassette);
                    if (!statusResult.Success)
                    {
                        UILogService.AddErrorLog($"状态交换失败: {statusResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {statusResult.Message}");
                        return (false, $"GetWafer失败: {statusResult.Message}");
                    }
                    UILogService.AddSuccessLog($"状态交换{sourceStationType}到挡板成功");
                    */
                }

                // 6. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("R轴归零");
                    var retractResult = await ZeroRAxisAsync(cmdService, cancellationToken);
                    if (!retractResult.Success)
                    {
                        UILogService.AddErrorLog($"R轴归零失败: {retractResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"获取晶圆失败: {retractResult.Message}");
                        return (false, $"GetWafer失败: {retractResult.Message}");
                    }
                    UILogService.AddSuccessLog("R轴归零成功");

                    switch (sourceStationType)
                    {
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            UILogService.AddLog($"{sourceStationType} 顶针再次下降取片完成");
                            break;

                        case EnuLocationStationType.Cassette:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            break;
                    }
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"成功从{sourceStationType}获取晶圆");
                return (true, $"成功从{sourceStationType}获取晶圆");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"从{sourceStationType}获取晶圆异常: {ex.Message}");
                return (false, $"GetWafer操作异常: {ex.Message}");
            }
        }

        #endregion 取晶圆操作

        #region 放晶圆操作

        /*
            Robot取放片操作规则详细说明：

            ═══════════════════════════════════════════════════════════════════════════════════════
            1. 工艺腔体 (ChamberA、ChamberB) - 顶针升降模式
            ═══════════════════════════════════════════════════════════════════════════════════════

            特点：R轴伸入腔体的Z轴高度固定，取放片通过腔体内部顶针升降机构完成

            【取片流程】：
            ① 预备阶段：确认腔体内有Wafer，顶针处于下降位置
            ② 顶针上升：腔体内顶针上升，将Wafer顶起到取片高度
            ③ Robot进入：T轴旋转到目标腔体方向，R轴伸入腔体到固定取片位置
            ④ 顶针下降：顶针缓慢下降，将Wafer平稳放置到Robot机械臂上
            ⑤ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑥ 完成取片：Robot机械臂上现在承载着Wafer

            【放片流程】：
            ① 预备阶段：确认腔体内无Wafer，顶针处于下降位置，Robot机械臂上有Wafer
            ② Robot进入：T轴旋转到目标腔体方向，R轴伸入腔体到固定放片位置
            ③ 顶针上升：顶针上升，从Robot机械臂下方托起Wafer
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 顶针下降：顶针下降到工艺位置，将Wafer放置到腔体内
            ⑥ 完成放片：Wafer现在位于腔体内，Robot机械臂空载

            ═══════════════════════════════════════════════════════════════════════════════════════
            2. 晶圆盒 (Cassette) - Z轴升降模式
            ═══════════════════════════════════════════════════════════════════════════════════════

            特点：R轴伸入晶圆盒的深度固定，取放片通过Robot的Z轴上下移动完成

            【取片流程】：
            ① 预备阶段：确认目标Slot有Wafer，Z轴移动到该Slot的取片高度
            ② Robot进入：T轴旋转到Cassette方向，R轴伸入晶圆盒到固定深度
            ③ Z轴上升：Z轴向上移动，Robot机械臂从Wafer下方托起
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 完成取片：Robot机械臂上现在承载着Wafer

            【放片流程】：
            ① 预备阶段：确认目标Slot为空，Robot机械臂上有Wafer，Z轴移动到该Slot上方的放片高度
            ② Robot进入：T轴旋转到Cassette方向，R轴伸入晶圆盒到固定深度
            ③ Z轴下降：Z轴向下移动，将Wafer平稳放置到目标Slot中
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 完成放片：Wafer现在位于目标Slot中，Robot机械臂空载

            ═══════════════════════════════════════════════════════════════════════════════════════
            3. 冷却腔 (CoolingTop、CoolingBottom) - Z轴升降模式
            ═══════════════════════════════════════════════════════════════════════════════════════

            特点：与Cassette操作原理相同，通过Robot的Z轴上下移动完成取放片
            冷却腔有两个独立的层级：CoolingTop（上层）和CoolingBottom（下层）

            层级区分：
            • T轴和R轴：只需要旋转到CoolingChamber方向，不区分上下层
            • Z轴：需要精确控制高度，区分CoolingTop和CoolingBottom两个不同的高度位置

            【CoolingTop（上层）取片流程】：
            ① 预备阶段：确认CoolingTop有Wafer，Z轴移动到上层取片高度
            ② Robot进入：T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度
            ③ Z轴上升：Z轴向上移动，Robot机械臂从Wafer下方托起
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 完成取片：Robot机械臂上现在承载着Wafer

            【CoolingBottom（下层）取片流程】：
            ① 预备阶段：确认CoolingBottom有Wafer，Z轴移动到下层取片高度
            ② Robot进入：T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度
            ③ Z轴上升：Z轴向上移动，Robot机械臂从Wafer下方托起
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 完成取片：Robot机械臂上现在承载着Wafer

            【CoolingTop（上层）放片流程】：
            ① 预备阶段：确认CoolingTop为空，Robot机械臂上有Wafer，Z轴移动到上层放片高度
            ② Robot进入：T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度
            ③ Z轴下降：Z轴向下移动，将Wafer平稳放置到上层位置
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 完成放片：Wafer现在位于CoolingTop，Robot机械臂空载

            【CoolingBottom（下层）放片流程】：
            ① 预备阶段：确认CoolingBottom为空，Robot机械臂上有Wafer，Z轴移动到下层放片高度
            ② Robot进入：T轴旋转到CoolingChamber方向，R轴伸入冷却腔到固定深度
            ③ Z轴下降：Z轴向下移动，将Wafer平稳放置到下层位置
            ④ Robot退出：R轴缩回到原点位置，T轴旋转到安全角度
            ⑤ 完成放片：Wafer现在位于CoolingBottom，Robot机械臂空载

            ═══════════════════════════════════════════════════════════════════════════════════════
            重要安全注意事项：
            ═══════════════════════════════════════════════════════════════════════════════════════
            • 所有操作前必须进行InterLock安全检查
            • 确认目标位置状态（有片/无片）与操作类型匹配
            • Robot轴运动过程中需要实时监控位置传感器状态
            • 异常情况下立即停止操作并报警
            • 每个步骤完成后需要确认传感器状态反馈

         */

        /// <summary>
        /// 将晶圆放置到指定位置
        /// 支持工艺腔体（顶针升降模式）和晶圆盒/冷却腔（Z轴升降模式）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型（Nose端或Smooth端）</param>
        /// <param name="targetStationType">目标站点类型</param>
        /// <param name="slotNumber">Slot号（对于晶圆盒和冷却腔）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> PutWaferAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType targetStationType,
            int slotNumber,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录放置晶圆流程
            UILogService.AddLogAndIncreaseIndent($"开始将晶圆放置到{targetStationType} (端口: {endType}, Slot: {slotNumber})");

            try
            {
                // 执行InterLock安全检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行InterLock安全检查");
                    var interlockResult = await CheckInterLockAsync(cmdService, targetStationType, cancellationToken);
                    if (!interlockResult.Success)
                    {
                        UILogService.AddErrorLog($"InterLock检查失败: {interlockResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {interlockResult.Message}");
                        return (false, $"PutWafer失败: {interlockResult.Message}");
                    }
                    UILogService.AddSuccessLog("InterLock安全检查通过");
                }

                // 确认目标位置无晶圆
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"确认{targetStationType}位置无晶圆");
                    var waferCheckResult = await CheckWaferPresenceAsync(cmdService, targetStationType, slotNumber, false, cancellationToken);
                    if (!waferCheckResult.Success)
                    {
                        UILogService.AddErrorLog($"目标位置晶圆检查失败: {waferCheckResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {waferCheckResult.Message}");
                        return (false, $"PutWafer失败: {waferCheckResult.Message}");
                    }
                    UILogService.AddSuccessLog($"{targetStationType}位置确认无晶圆");
                }

                // 确认Robot机械臂上有晶圆
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("确认Robot机械臂上有晶圆");
                    var armWaferCheckResult = await CheckRobotArmWaferAsync(cmdService, endType, true, cancellationToken);
                    if (!armWaferCheckResult.Success)
                    {
                        UILogService.AddErrorLog($"Robot机械臂晶圆检查失败: {armWaferCheckResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {armWaferCheckResult.Message}");
                        return (false, $"PutWafer失败: {armWaferCheckResult.Message}");
                    }
                    UILogService.AddSuccessLog("Robot机械臂上确认有晶圆");
                }

                // 1. 移动T轴到目标站点位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动T轴到{targetStationType}位置");
                    var tAxisResult = await MoveTAxisToLocationAsync(cmdService, endType, targetStationType, cancellationToken);
                    if (!tAxisResult.Success)
                    {
                        UILogService.AddErrorLog($"T轴移动失败: {tAxisResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {tAxisResult.Message}");
                        return (false, $"PutWafer失败: {tAxisResult.Message}");
                    }
                    UILogService.AddSuccessLog($"T轴移动到{targetStationType}位置成功");
                }

                // 2. 移动Z轴到放片高度（根据目标类型确定高度）
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"移动Z轴到{targetStationType}放片高度");
                    var zAxisResult = await MoveZAxisToPutPositionAsync(cmdService, endType, targetStationType, slotNumber, cancellationToken);
                    if (!zAxisResult.Success)
                    {
                        UILogService.AddErrorLog($"Z轴移动到放片高度失败: {zAxisResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {zAxisResult.Message}");
                        return (false, $"PutWafer失败: {zAxisResult.Message}");
                    }
                    UILogService.AddSuccessLog($"Z轴移动到{targetStationType}放片高度成功");
                }

                // 3. 伸展R轴到目标站点位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"伸展R轴到{targetStationType}位置");
                    var rAxisResult = await MoveRAxisToLocationAsync(cmdService, endType, targetStationType, cancellationToken);
                    if (!rAxisResult.Success)
                    {
                        UILogService.AddErrorLog($"R轴伸展失败: {rAxisResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {rAxisResult.Message}");
                        return (false, $"PutWafer失败: {rAxisResult.Message}");
                    }
                    UILogService.AddSuccessLog($"R轴伸展到{targetStationType}位置成功");
                }

                // 4. 根据位置类型执行不同的放片操作
                using (UILogService.CreateIndentScope())
                {
                    switch (targetStationType)
                    {
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            // 工艺腔体模式：顶针升降放片
                            UILogService.AddLogAndIncreaseIndent($"执行{targetStationType}顶针升降放片操作");

                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog($"等待{targetStationType}顶针上升，从Robot机械臂下方托起晶圆");
                                await Task.Delay(100, cancellationToken); // 等待顶针上升
                                UILogService.AddLog("顶针上升完成");
                            }

                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog($"等待{targetStationType}顶针下降到工艺位置");
                                await Task.Delay(100, cancellationToken); // 等待顶针下降
                                UILogService.AddLog("顶针下降完成");
                            }

                            UILogService.DecreaseIndentAndAddSuccessLog($"{targetStationType}顶针升降放片操作完成");
                            break;

                        case EnuLocationStationType.Cassette:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            // 晶圆盒/冷却腔模式：Z轴升降放片
                            UILogService.AddLogAndIncreaseIndent($"执行{targetStationType}Z轴升降放片操作");

                            using (UILogService.CreateIndentScope())
                            {
                                UILogService.AddLog($"Z轴下降到{targetStationType}取片位置，将晶圆放置到目标位置");
                                var zAxisDownResult = await MoveZAxisToGetPositionAsync(cmdService, endType, targetStationType, slotNumber, cancellationToken);
                                if (!zAxisDownResult.Success)
                                {
                                    UILogService.AddErrorLog($"Z轴下降到取片位置失败: {zAxisDownResult.Message}");
                                    UILogService.DecreaseIndentAndAddErrorLog($"Z轴升降放片操作失败: {zAxisDownResult.Message}");
                                    UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {zAxisDownResult.Message}");
                                    return (false, $"PutWafer失败: {zAxisDownResult.Message}");
                                }
                                UILogService.AddSuccessLog($"Z轴下降到{targetStationType}取片位置成功，晶圆已放置");
                            }

                            UILogService.DecreaseIndentAndAddSuccessLog($"{targetStationType}Z轴升降放片操作完成");
                            break;

                        default:
                            UILogService.AddErrorLog($"不支持的目标站点类型: {targetStationType}");
                            UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: 不支持的目标站点类型 {targetStationType}");
                            return (false, $"PutWafer失败: 不支持的目标站点类型 {targetStationType}");
                    }
                }

                // 5. 执行状态交换（挡板到目标站点）
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"Todo：需要执行状态交换: {endType}到{targetStationType}");
                    /*
                      //执行Wafer状态交换，后面带优化
                      var statusResult = await ExecuteStatusExchangeAsync(cmdService, endType, EnuLocationStationType.Cassette, targetStationType);
                      if (!statusResult.Success)
                      {
                          UILogService.AddErrorLog($"状态交换失败: {statusResult.Message}");
                          UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {statusResult.Message}");
                          return (false, $"PutWafer失败: {statusResult.Message}");
                      }
                      UILogService.AddSuccessLog($"状态交换挡板到{targetStationType}成功");
                     */
                }

                // 6. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("R轴归零");
                    var retractResult = await ZeroRAxisAsync(cmdService, cancellationToken);
                    if (!retractResult.Success)
                    {
                        UILogService.AddErrorLog($"R轴归零失败: {retractResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"放置晶圆失败: {retractResult.Message}");
                        return (false, $"PutWafer失败: {retractResult.Message}");
                    }
                    UILogService.AddSuccessLog("R轴归零成功");

                    switch (targetStationType)
                    {
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            UILogService.AddLog($"{targetStationType} 顶针再次下降放片完成");
                            break;

                        case EnuLocationStationType.Cassette:
                        case EnuLocationStationType.CoolingTop:
                        case EnuLocationStationType.CoolingBottom:
                            break;
                    }
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"✅ 成功将晶圆放置到{targetStationType}");
                return (true, $"成功将晶圆放置到{targetStationType}");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"将晶圆放置到{targetStationType}异常: {ex.Message}");
                return (false, $"PutWafer操作异常: {ex.Message}");
            }
        }

        #endregion 放晶圆操作

        #region 状态交换操作

        /// <summary>
        /// 执行状态交换（从源端到目标端）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="fromStationType">源站点类型</param>
        /// <param name="toStationType">目标站点类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> ExecuteStatusExchangeAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType fromStationType,
            EnuLocationStationType toStationType,
            CancellationToken cancellationToken)
        {
            try
            {
                UILogService.AddLog($"执行状态交换: {fromStationType}到{toStationType} (端口: {endType})...");

                // 将站点类型转换为对应的站点索引值
                int fromStationIndex = ConvertStationTypeToIndex(endType, fromStationType);
                int toStationIndex = ConvertStationTypeToIndex(endType, toStationType);
                UILogService.AddLog($"站点索引: {fromStationType}={fromStationIndex}, {toStationType}={toStationIndex}");

                // 由于EnuRobotCmdIndex中没有直接的状态交换命令，
                // 我们可以使用基本的移动命令来模拟状态交换操作

                // 1. 首先移动到源站点位置
                var moveResult = await cmdService.ExecuteRobotCommandAsync(
                    EnuRobotCmdIndex.MoveMotor,  // 使用通用的移动电机命令
                    (ushort)fromStationIndex,     // 源站点索引作为第一个参数（转换为ushort）
                    (ushort)toStationIndex,       // 目标站点索引作为第二个参数（转换为ushort）
                    (ushort)endType,              // 端口类型（转换为ushort）
                    (ushort)1);                   // 操作类型：1表示状态交换（转换为ushort）

                if (moveResult.ReturnInfo != 0)
                {
                    UILogService.AddErrorLog($"状态交换失败: {moveResult.Response}, 错误码: {moveResult.ReturnInfo}");
                    return (false, $"状态交换失败: {moveResult.Response}, 错误码: {moveResult.ReturnInfo}");
                }

                UILogService.AddSuccessLog($"状态交换{fromStationType}到{toStationType}成功");
                return (true, $"状态交换{fromStationType}到{toStationType}成功");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"状态交换异常: {ex.Message}");
                return (false, $"状态交换异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 将站点类型转换为对应的站点索引值
        /// </summary>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>站点索引值</returns>
        private static int ConvertStationTypeToIndex(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            // 根据端口类型和站点类型返回对应的站点索引值
            // 这些索引值应与设备控制系统中定义的站点索引一致
            switch (endType)
            {
                case EnuRobotEndType.Nose:
                    switch (stationType)
                    {
                        case EnuLocationStationType.Cassette:
                            return 1;  // Nose端挡板
                        case EnuLocationStationType.ChamberA:
                            return 2;  // Nose端工艺腔室A
                        case EnuLocationStationType.ChamberB:
                            return 3;  // Nose端工艺腔室B
                        case EnuLocationStationType.CoolingTop:
                            return 4;  // Nose端冷却TOP
                        case EnuLocationStationType.CoolingBottom:
                            return 5;  // Nose端冷却BOTTOM
                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                case EnuRobotEndType.Smooth:
                    switch (stationType)
                    {
                        case EnuLocationStationType.Cassette:
                            return 6;  // Smooth端挡板
                        case EnuLocationStationType.ChamberA:
                            return 7;  // Smooth端工艺腔室A
                        case EnuLocationStationType.ChamberB:
                            return 8;  // Smooth端工艺腔室B
                        case EnuLocationStationType.CoolingTop:
                            return 9;  // Smooth端冷却TOP
                        case EnuLocationStationType.CoolingBottom:
                            return 10; // Smooth端冷却BOTTOM
                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                default:
                    throw new ArgumentException($"不支持的端口类型: {endType}");
            }
        }

        #endregion 状态交换操作

        #endregion 二级命令 - 晶圆取放操作

        #region 三级命令 - 基础轴控制操作

        #region 机器人初始化

        /// <summary>
        /// 初始化机器人（包括三轴归零）
        /// Z轴初始化尤为重要，因为Z轴移动是取放晶圆的关键操作
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> InitializeRobotAsync(
            this IS200McuCmdService cmdService,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录机器人初始化流程
            UILogService.AddLogAndIncreaseIndent("开始初始化机器人（三轴归零）");

            try
            {
                // 1. T轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行T轴归零");
                    var tResult = await ZeroTAxisAsync(cmdService, cancellationToken);
                    if (!tResult.Success)
                    {
                        UILogService.AddErrorLog($"T轴归零失败: {tResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {tResult.Message}");
                        return tResult; // 返回错误信息
                    }
                    UILogService.AddSuccessLog("T轴归零成功");
                }

                // 2. R轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行R轴归零");
                    var rResult = await ZeroRAxisAsync(cmdService, cancellationToken);
                    if (!rResult.Success)
                    {
                        UILogService.AddErrorLog($"R轴归零失败: {rResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {rResult.Message}");
                        return rResult; // 返回错误信息
                    }
                    UILogService.AddSuccessLog("R轴归零成功");
                }

                // 3. Z轴归零
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行Z轴归零");
                    var zResult = await ZeroZAxisAsync(cmdService, cancellationToken);
                    if (!zResult.Success)
                    {
                        UILogService.AddErrorLog($"Z轴归零失败: {zResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化失败: {zResult.Message}");
                        return zResult; // 返回错误信息
                    }
                    UILogService.AddSuccessLog("Z轴归零成功");
                }

                UILogService.DecreaseIndentAndAddSuccessLog("机器人三轴归零成功");
                return (true, "机器人三轴归零成功");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"机器人初始化异常: {ex.Message}");
                return (false, $"机器人初始化异常: {ex.Message}");
            }
        }

        #endregion 机器人初始化

        #region T轴操作

        #region T轴基础移动

        /// <summary>
        /// 移动T轴到指定位置
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="positionValue">位置步数值（RP1-RP9对应的具体步数）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveTAxisToPositionAsync(
            this IS200McuCmdService cmdService,
            int positionValue,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录T轴基础移动
            UILogService.AddLogAndIncreaseIndent($"执行T轴移动到{positionValue}步数位置");

            try
            {
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"发送T轴移动命令，目标位置: {positionValue}");
                    var result = await cmdService.ExecuteRobotTAxisCommandAsync(positionValue);

                    if (result.ReturnInfo == 0)
                    {
                        UILogService.AddSuccessLog($"T轴移动命令执行成功");
                        UILogService.DecreaseIndentAndAddSuccessLog($"T轴移动到{positionValue}步数位置成功");
                        return (true, $"T轴移动到{positionValue}步数位置成功");
                    }
                    else
                    {
                        UILogService.AddErrorLog($"T轴移动命令执行失败: {result.Response}, 错误码: 0x{result.ReturnInfo:X4}");
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴移动失败: 错误码 0x{result.ReturnInfo:X4}");
                        return (false, $"T轴移动失败: {result.Response}, 错误码: {result.ReturnInfo}");
                    }
                }
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"T轴移动异常: {ex.Message}");
                return (false, $"T轴移动异常: {ex.Message}");
            }
        }

        #endregion T轴基础移动

        #region T轴位置旋转

        /// <summary>
        /// T轴位置旋转
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveTAxisToLocationAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType stationType,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录T轴旋转流程
            UILogService.AddLogAndIncreaseIndent($"开始执行{endType}端T轴旋转到{stationType}位置");

            try
            {
                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock获取状态
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Robot状态 (MRS1~MRS3)");
                    if (!CheckRobotStatusForOperation($"机器人{endType}端旋转到{stationType}位置"))
                    {
                        UILogService.AddErrorLog($"机器人状态不允许执行{endType}端旋转到{stationType}位置操作");
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转失败: 机器人状态不允许操作");
                        return (false, $"机器人状态不允许执行{endType}端旋转到{stationType}位置操作");
                    }
                    UILogService.AddSuccessLog("Robot状态检查通过");
                }

                // 2. 检查T轴是否已在目标位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"检查{endType}端T轴是否已在{stationType}位置");
                    bool blHasToLocation = false;
                    switch (endType)
                    {
                        case EnuRobotEndType.Smooth:
                            // 直接比较枚举值，判断Smooth端是否已到目的地
                            blHasToLocation = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status.EnuTAxisSmoothDestination.GetTAxisLocation() == stationType.GetTAxisLocation();
                            if (blHasToLocation)
                            {
                                UILogService.AddLog($"Smooth端已在{stationType}位置");
                            }
                            break;

                        case EnuRobotEndType.Nose:
                            // 直接比较枚举值，判断Nose端是否已到目的地
                            blHasToLocation = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status.EnuTAxisNoseDestination.GetTAxisLocation() == stationType.GetTAxisLocation();
                            if (blHasToLocation)
                            {
                                UILogService.AddLog($"Nose端已在{stationType}位置");
                            }
                            break;
                    }

                    if (blHasToLocation)
                    {
                        _msg = $"T轴已到达{stationType}位置，无需移动";
                        UILogService.AddSuccessLog(_msg);
                        UILogService.DecreaseIndentAndAddSuccessLog($"T轴旋转完成: {_msg}");
                        return (true, _msg);
                    }
                    UILogService.AddLog($"T轴未在{stationType}位置，需要执行旋转操作");
                }

                // 3. 检查R轴是否在原点位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查R轴是否在原点位置 (RS18)");
                    if (!_interLock.SubsystemStatus.Robot.Status.RAxisIsZeroPosition)//Todo:现场报错，停止运行，R轴不在原点位置 (RS18)：1、前面 R轴归零没有实际成功；2、或者状态表RAxisIsZeroPosition更新不及时
                    {
                        _msg = $"R轴未在原点位置，无法移动T轴到{stationType}位置，请先归零R轴";
                        UILogService.AddErrorLog(_msg);
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转失败: {_msg}");
                        return (false, _msg);
                    }
                    UILogService.AddSuccessLog("R轴在原点位置，允许T轴旋转");
                }

                // 4. Shuttle滑出传感器检查 (SPS11检查)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Shuttle滑出传感器状态 (SPS11)");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                // 5. 移动Z轴到旋转高度 (AR39-RPS27)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("移动Z轴到Robot旋转高度 (AR39-RPS27)");
                    int zforRobotRotation = _interLock.SubsystemConfigure.Robot.RPS27_ZAxisHeightForRobotRotation?.Value ?? 0;
                    var result = await MoveZAxisToPositionAsync(cmdService, zforRobotRotation, cancellationToken);

                    if (!result.Success)
                    {
                        UILogService.AddErrorLog($"Robot旋转时Z轴高度失败: {result.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转失败: {result.Message}");
                        return result;
                    }
                    UILogService.AddSuccessLog("Robot旋转时Z轴高度设置成功");
                }

                // 6. 执行T轴旋转到目标位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"执行T轴旋转到{stationType}位置");
                    int positionValue = RobotPositionParameters.GetTAxisPosition(endType, stationType);
                    var result = await MoveTAxisToPositionAsync(cmdService, positionValue, cancellationToken);

                    if (!result.Success)
                    {
                        UILogService.AddErrorLog($"T轴旋转失败: {result.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转失败: {result.Message}");
                        return result;
                    }
                    UILogService.AddSuccessLog($"T轴旋转到{stationType}位置成功");
                }

                // 7. 旋转完成后再次检查Shuttle传感器
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("旋转完成后检查Shuttle滑出传感器状态");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"✅ T轴成功旋转到{stationType}位置");
                return (true, $"T轴成功旋转到{stationType}位置");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"T轴旋转异常: {ex.Message}");
                return (false, $"T轴移动异常: {ex.Message}");
            }
        }

        #endregion T轴位置旋转

        #region T轴归零

        /// <summary>
        /// T轴归零 - 根据AR09逻辑实现完整的T轴归零流程
        /// 主要利用InterLock系统获取状态和配置信息
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> ZeroTAxisAsync(
            this IS200McuCmdService cmdService,
            CancellationToken cancellationToken)
        {
            try
            {
                UILogService.AddLog("开始执行T轴归零操作(AR09)...");

                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock获取状态
                if (!CheckRobotStatusForOperation("T轴归零"))
                {
                    return (false, "机器人状态不允许执行T轴归零操作");
                }

                // 2. 获取当前RTZ轴位置 - 使用InterLock的RTZAxisPosition
                var currentPosition = await _interLock.RTZAxisPosition.GetCurrentRTZSteps();
                UILogService.AddLog($"当前RTZ轴位置: T={currentPosition.TAxisStep}, R={currentPosition.RAxisStep}, Z={currentPosition.ZAxisStep}");

                // 3. 检查是否已经在T轴零位 (RS9) - 使用InterLock Robot当前T轴状态判断
                var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                if (robotStatus.TAxisIsZeroPosition)
                {
                    UILogService.AddSuccessLog("T轴已在零位(RS9)，归零完成");
                    return (true, "T轴已在零位，归零完成");
                }

                // 4. 执行归零流程
                UILogService.AddLog("T轴不在零位，开始执行归零流程...");
                /*
                T轴旋转当前站点摆正处理：
                1. 若在chamber中，先摆正再缩回
                2. 若在L/L中，此时摆正时必然不会撞到slit door
                处理逻辑如下：
                1、比较T轴当前位置smooth\nose到ChamberA\ChamberB、cooling、cassette哪个位置最近
                2、计算摆正偏差值 X=min(ABS(RTF-RP1),ABS(RTF-RP2),ABS(RTF-RP3),ABS(RTF-RP4),ABS(RTF-RP5),ABS(RTF-RP6),ABS(RFT-RP7),ABS(RTF-RP8))【RTF值的当前R轴位置】
                3、如果X！=0，执行Robot，T轴旋转到RP X
                */

                // 计算摆正偏差
                var alignmentResult = CalculateAlignmentDeviation(currentPosition.TAxisStep);//实现计算摆正偏差的方法

                if (alignmentResult.NeedsAlignment)//使用标志位判断是否需要摆正，解决目标位置为0的问题
                {
                    // 执行T轴旋转到最接近的标准位置
                    UILogService.AddLog($"T轴旋转到位置{alignmentResult.TargetPosition}进行摆正...");
                    var rotateResult = await MoveTAxisToPositionAsync(cmdService, alignmentResult.TargetPosition, cancellationToken);

                    if (!rotateResult.Success)
                    {
                        UILogService.AddErrorLog($"T轴摆正失败: {rotateResult.Message}");
                        return rotateResult;
                    }
                    UILogService.AddSuccessLog("T轴摆正完成");
                }
                else
                {
                    // 异常情况下返回不需要摆正
                    if (alignmentResult.ClosestParameterName.StartsWith("异常"))
                    {
                        UILogService.AddErrorLog($"T轴计算当前站点摆正偏差出现异常：{alignmentResult.ClosestParameterName}");
                        return (false, $"T轴计算当前站点摆正偏差出现异常：{alignmentResult.ClosestParameterName}");
                    }
                    else
                    {
                        UILogService.AddLog("T轴已在标准位置，无需摆正");
                    }
                }

                // int tAxisZeroPosition = _interLock.SubsystemConfigure.Robot.RP9_TAxisZero.Value;
                // if (Math.Abs(currentPosition.TAxisStep - tAxisZeroPosition) <= 100) // 允许100步的误差
                // {
                //     UILogService.AddSuccessLog("T轴已在零位(RS9)，归零完成");
                //     return (true, "T轴已在零位，归零完成");
                // }

                // 4.1 R轴归零 (AR19) - 确保R轴在零位
                UILogService.AddLog("执行R轴归零(AR19)...");
                var rAxisZeroResult = await ZeroRAxisAsync(cmdService, cancellationToken);
                if (!rAxisZeroResult.Success)
                {
                    UILogService.AddErrorLog($"R轴归零失败: {rAxisZeroResult.Message}");
                    return rAxisZeroResult;
                }

                // 4.2 Z轴移动到旋转位置 (AR39-RPS27) - 使用InterLock获取旋转高度
                UILogService.AddLog("移动Z轴到旋转位置(AR39-RPS27)...");
                int rotationHeight = _interLock.SubsystemConfigure.Robot.RPS27_ZAxisHeightForRobotRotation.Value;
                var zAxisRotationResult = await MoveZAxisToPositionAsync(cmdService, rotationHeight, cancellationToken);
                if (!zAxisRotationResult.Success)
                {
                    UILogService.AddErrorLog($"Z轴移动到旋转位置失败: {zAxisRotationResult.Message}");
                    return zAxisRotationResult;
                }

                // 4.3 移动T轴到零位 (AR10-RP9)
                UILogService.AddLog("移动T轴到零位(AR10-RP9)...");
                int tAxisZeroPosition = _interLock.SubsystemConfigure.Robot.RP9_TAxisZero.Value;
                var tAxisZeroResult = await MoveTAxisToPositionAsync(cmdService, tAxisZeroPosition, cancellationToken);
                if (!tAxisZeroResult.Success)
                {
                    UILogService.AddErrorLog($"T轴移动到零位失败: {tAxisZeroResult.Message}");
                    return tAxisZeroResult;
                }

                // 4.4 Shuttle滑出传感器检查 (SPS11检查) - 使用InterLock获取传感器状态
                var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                if (!shuttleSensorCheck.Success)
                {
                    UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                    return shuttleSensorCheck;
                }

                UILogService.AddSuccessLog("T轴归零操作完成");
                return (true, "T轴归零操作完成");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"T轴归零异常: {ex.Message}");
                return (false, $"T轴归零异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查机器人状态是否允许执行操作 - 使用InterLock系统
        /// </summary>
        /// <param name="operationName">操作名称</param>
        /// <returns>是否允许执行</returns>
        private static bool CheckRobotStatusForOperation(string operationName)
        {
            try
            {
                var robotStatus = _interLock.SubsystemStatus.Robot.Status;

                // MRS1 IDLE - 允许执行
                if (robotStatus.EnuRobotStatus == EnuRobotStatus.Idle)
                {
                    UILogService.AddLog($"机器人状态检查通过: MRS1 IDLE，允许执行{operationName}");
                    return true;
                }

                // MRS2 BUSY - RA1 ALARM
                if (robotStatus.EnuRobotStatus == EnuRobotStatus.Busy)
                {
                    UILogService.AddErrorLog($"机器人状态检查失败: MRS2 BUSY - RA1 ALARM，无法执行{operationName}");
                    return false;
                }

                // MRS3 ALARM - RA2 ALARM
                if (robotStatus.EnuRobotStatus == EnuRobotStatus.Alarm)
                {
                    UILogService.AddErrorLog($"机器人状态检查失败: MRS3 ALARM - RA2 ALARM，无法执行{operationName}");
                    return false;
                }

                // 其他状态
                UILogService.AddErrorLog($"机器人状态未知: {robotStatus.EnuRobotStatus}，无法执行{operationName}");
                return false;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"机器人状态检查异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查Shuttle滑出传感器状态 - 使用InterLock系统获取传感器状态
        /// </summary>
        /// <returns>检查结果</returns>
        private static (bool Success, string Message) CheckShuttleSlideOutSensors()
        {
            try
            {
                UILogService.AddLog("开始检查Shuttle滑出传感器状态...");

                // 检查SPS11 - 后滑出传感器使能 (使用InterLock获取配置)
                var slideOutBackSensorEnable = _interLock.SubsystemConfigure.Shuttle.SPS11_SlideOutBackSensorEnable.BoolValue;
                UILogService.AddLog($"SPS11后滑出传感器使能状态: {slideOutBackSensorEnable}");

                if (slideOutBackSensorEnable)
                {
                    // SPS11=Y，需要检查shuttle DI19~DI20状态 (使用InterLock获取IO状态)
                    UILogService.AddLog("SPS11=Y，检查Shuttle滑出传感器DI19~DI20状态...");

                    bool di19Status = _interLock.IOInterface.Shuttle.SDI19_WaferSlideOutSensor3BL.Value;
                    bool di20Status = _interLock.IOInterface.Shuttle.SDI20_WaferSlideOutSensor4BR.Value;

                    UILogService.AddLog($"DI19状态: {(di19Status ? 1 : 0)}, DI20状态: {(di20Status ? 1 : 0)}");

                    // 根据AR09逻辑检查传感器状态组合
                    if (!di19Status && !di20Status) // DI19=0 and DI20=0
                    {
                        UILogService.AddSuccessLog("Shuttle传感器状态正常: DI19=0 and DI20=0");
                        return (true, "Shuttle传感器状态正常");
                    }
                    else if (!di19Status && di20Status) // DI19=0 and DI20=1
                    {
                        UILogService.AddErrorLog("Shuttle传感器状态异常: DI19=0 and DI20=1 - RA20 ALARM");
                        return (false, "Shuttle传感器异常，报警RA20");
                    }
                    else if (di19Status && !di20Status) // DI19=1 and DI20=0
                    {
                        UILogService.AddErrorLog("Shuttle传感器状态异常: DI19=1 and DI20=0 - RA19 ALARM");
                        return (false, "Shuttle传感器异常，报警RA19");
                    }
                    else // DI19=1 and DI20=1
                    {
                        UILogService.AddErrorLog("Shuttle传感器状态异常: DI19=1 and DI20=1 - RA21 ALARM");
                        return (false, "Shuttle传感器异常，报警RA21");
                    }
                }
                else
                {
                    // SPS11=N，跳过传感器检查
                    UILogService.AddLog("SPS11=N，跳过Shuttle传感器检查");
                    return (true, "SPS11=N，跳过传感器检查");
                }
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"Shuttle传感器检查异常: {ex.Message}");
                return (false, $"Shuttle传感器检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// T轴摆正偏差计算结果
        /// </summary>
        public class TAxisAlignmentResult
        {
            /// <summary>
            /// 是否需要摆正
            /// </summary>
            public bool NeedsAlignment { get; set; }

            /// <summary>
            /// 目标摆正位置（步数）
            /// </summary>
            public int TargetPosition { get; set; }

            /// <summary>
            /// 最小距离
            /// </summary>
            public int MinDistance { get; set; }

            /// <summary>
            /// 最接近的参数名称
            /// </summary>
            public string ClosestParameterName { get; set; }
        }

        /// <summary>
        /// 计算T轴摆正偏差的方法
        /// 比较当前T轴位置到所有预设位置（RP1-RP8）的距离，找到最接近的位置
        /// </summary>
        /// <param name="currentTAxisPosition">当前T轴位置</param>
        /// <returns>摆正计算结果，包含是否需要摆正和目标位置</returns>
        private static TAxisAlignmentResult CalculateAlignmentDeviation(int currentTAxisPosition)
        {
            try
            {
                UILogService.AddLog($"开始计算T轴摆正偏差，当前T轴位置: {currentTAxisPosition}");

                // 获取所有T轴位置参数 RP1-RP8
                var positionParameters = new Dictionary<string, int>
                {
                    ["RP1_T轴Smooth端到工艺腔室A"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberA),        // T轴Smooth端到工艺腔室A
                    ["RP2_T轴Smooth端到工艺腔室B"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Smooth, EnuLocationStationType.ChamberB),        // T轴Smooth端到工艺腔室B
                    ["RP3_T轴Smooth端到冷却腔"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Smooth, EnuLocationStationType.CoolingChamber), // T轴Smooth端到冷却腔（面向方向，无上下之分）
                    ["RP4_T轴Smooth端到晶圆盒"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Smooth, EnuLocationStationType.Cassette),        // T轴Smooth端到晶圆盒
                    ["RP5_T轴Nose端到工艺腔室A"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Nose, EnuLocationStationType.ChamberA),          // T轴Nose端到工艺腔室A
                    ["RP6_T轴Nose端到工艺腔室B"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Nose, EnuLocationStationType.ChamberB),          // T轴Nose端到工艺腔室B
                    ["RP7_T轴Nose端到冷却腔"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Nose, EnuLocationStationType.CoolingChamber),   // T轴Nose端到冷却腔（面向方向，无上下之分）
                    ["RP8_T轴Nose端到晶圆盒"] = RobotPositionParameters.GetTAxisPosition(EnuRobotEndType.Nose, EnuLocationStationType.Cassette)           // T轴Nose端到晶圆盒
                };

                // 计算到每个位置的距离，找到最小距离
                int minDistance = int.MaxValue;
                int closestPosition = 0;
                string closestParameterName = "";

                foreach (var parameter in positionParameters)
                {
                    int distance = Math.Abs(currentTAxisPosition - parameter.Value);
                    UILogService.AddLog($"{parameter.Key}位置: {parameter.Value}, 距离: {distance}");

                    if (distance < minDistance)
                    {
                        minDistance = distance;
                        closestPosition = parameter.Value;
                        closestParameterName = parameter.Key;
                    }
                }

                UILogService.AddLog($"最接近的位置: {closestParameterName}({closestPosition}), 最小距离: {minDistance}");

                // 创建返回结果
                var result = new TAxisAlignmentResult
                {
                    NeedsAlignment = minDistance > 0, // 距离大于0才需要摆正
                    TargetPosition = closestPosition,
                    MinDistance = minDistance,
                    ClosestParameterName = closestParameterName
                };

                if (result.NeedsAlignment)
                {
                    UILogService.AddLog($"需要摆正到位置: {closestParameterName}({closestPosition})，距离: {minDistance}");
                }
                else
                {
                    UILogService.AddSuccessLog("T轴已在正确位置，无需摆正");
                }

                return result;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"计算T轴摆正偏差异常: {ex.Message}");
                return new TAxisAlignmentResult
                {
                    NeedsAlignment = false,
                    TargetPosition = 0,
                    MinDistance = 0,
                    ClosestParameterName = $"异常：+{ex.Message}"
                }; // 异常情况下返回不需要摆正
            }
        }

        #endregion T轴归零

        #endregion T轴操作

        #region R轴操作

        #region R轴基础移动

        /// <summary>
        /// 移动R轴到指定位置（增强版：等待移动完成并验证位置）
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="positionValue">位置步数值（RP10-RP18对应的具体步数）</param>
        /// <param name="waitForCompletion">是否等待移动完成（默认true）</param>
        /// <param name="verifyPosition">是否验证最终位置（默认true）</param>
        /// <param name="timeoutMs">等待超时时间（毫秒，默认10秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveRAxisToPositionAsync(
            this IS200McuCmdService cmdService,
            int positionValue,
            bool waitForCompletion,
            bool verifyPosition,
            int timeoutMs,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录R轴基础移动
            UILogService.AddLogAndIncreaseIndent($"执行R轴移动到{positionValue}步数位置");

            try
            {
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"发送R轴移动命令，目标位置: {positionValue}");
                    var result = await cmdService.ExecuteRobotRAxisCommandAsync(positionValue);

                    if (result.ReturnInfo != 0)
                    {
                        UILogService.AddErrorLog($"R轴移动命令执行失败: {result.Response}, 错误码: 0x{result.ReturnInfo:X4}");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴移动失败: 错误码 0x{result.ReturnInfo:X4}");
                        return (false, $"R轴移动失败: {result.Response}, 错误码: {result.ReturnInfo}");
                    }

                    UILogService.AddSuccessLog($"R轴移动命令发送成功");

                    // 如果需要等待移动完成
                    if (waitForCompletion)
                    {
                        UILogService.AddLog("等待R轴移动完成...");
                        var waitResult = await WaitForRAxisMovementCompletionAsync(positionValue, timeoutMs, cancellationToken);
                        if (!waitResult.Success)
                        {
                            UILogService.AddErrorLog($"等待R轴移动完成失败: {waitResult.Message}");
                            UILogService.DecreaseIndentAndAddErrorLog($"R轴移动失败: {waitResult.Message}");
                            return waitResult;
                        }
                        UILogService.AddSuccessLog("R轴移动完成");
                    }

                    // 如果需要验证位置
                    if (verifyPosition)
                    {
                        UILogService.AddLog("验证R轴最终位置...");
                        var verifyResult = await VerifyRAxisPositionAsync(positionValue, cancellationToken);
                        if (!verifyResult.Success)
                        {
                            UILogService.AddWarningLog($"位置验证失败: {verifyResult.Message}");
                            // 位置验证失败不一定是致命错误，可能是容差范围内的差异
                        }
                        else
                        {
                            UILogService.AddSuccessLog("R轴位置验证通过");
                        }
                    }

                    UILogService.DecreaseIndentAndAddSuccessLog($"R轴移动到{positionValue}步数位置成功");
                    return (true, $"R轴移动到{positionValue}步数位置成功");
                }
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"R轴移动异常: {ex.Message}");
                return (false, $"R轴移动异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 等待R轴移动完成
        /// </summary>
        /// <param name="targetPosition">目标位置步数</param>
        /// <param name="timeoutMs">超时时间（毫秒）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>等待结果</returns>
        private static async Task<(bool Success, string Message)> WaitForRAxisMovementCompletionAsync(
            int targetPosition, int timeoutMs, CancellationToken cancellationToken)
        {
            try
            {
                var startTime = DateTime.Now;
                const int tolerance = 0; // 位置容差：要求精确到位
                const int checkIntervalMs = 100; // 检查间隔

                while ((DateTime.Now - startTime).TotalMilliseconds < timeoutMs)
                {
                    // 获取当前R轴位置
                    var currentPosition = await _interLock.RTZAxisPosition.GetCurrentRTZSteps();

                    // 检查是否到达目标位置（在容差范围内）
                    if (Math.Abs(currentPosition.RAxisStep - targetPosition) <= tolerance)
                    {
                        // 强制更新状态表以确保状态同步
                        await ForceUpdateRobotStatusAsync(cancellationToken);
                        return (true, $"R轴已到达目标位置 {targetPosition}，当前位置: {currentPosition.RAxisStep}");
                    }

                    // 等待一段时间后再次检查
                    await Task.Delay(checkIntervalMs, cancellationToken);
                }

                // 超时
                var finalPosition = await _interLock.RTZAxisPosition.GetCurrentRTZSteps();
                return (false, $"等待R轴移动完成超时，目标位置: {targetPosition}，当前位置: {finalPosition.RAxisStep}");
            }
            catch (Exception ex)
            {
                return (false, $"等待R轴移动完成异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 验证R轴位置是否正确
        /// </summary>
        /// <param name="expectedPosition">期望位置步数</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>验证结果</returns>
        private static async Task<(bool Success, string Message)> VerifyRAxisPositionAsync(int expectedPosition, CancellationToken cancellationToken)
        {
            try
            {
                // 强制更新状态表
                await ForceUpdateRobotStatusAsync(cancellationToken);

                // 获取当前位置
                var currentPosition = await _interLock.RTZAxisPosition.GetCurrentRTZSteps();
                const int tolerance = 0; // 位置容差：要求精确到位

                bool isAtExpectedPosition = Math.Abs(currentPosition.RAxisStep - expectedPosition) <= tolerance;

                if (isAtExpectedPosition)
                {
                    return (true, $"R轴位置验证通过，期望: {expectedPosition}，实际: {currentPosition.RAxisStep}");
                }
                else
                {
                    return (false, $"R轴位置验证失败，期望: {expectedPosition}，实际: {currentPosition.RAxisStep}，差值: {Math.Abs(currentPosition.RAxisStep - expectedPosition)}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"验证R轴位置异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 强制更新Robot状态表
        /// </summary>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>更新结果</returns>
        private static async Task<bool> ForceUpdateRobotStatusAsync(CancellationToken cancellationToken)
        {
            try
            {
                // 这里需要触发状态表的强制更新
                // 可以通过调用状态更新方法或发送更新信号来实现

                // 等待一小段时间让状态表更新
                await Task.Delay(50, cancellationToken);

                return true;
            }
            catch (Exception ex)
            {
                UILogService.AddWarningLog($"强制更新Robot状态表失败: {ex.Message}");
                return false;
            }
        }

        #endregion R轴基础移动

        #region R轴位置移动

        /// <summary>
        /// R轴伸展到指定位置
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveRAxisToLocationAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType stationType,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录R轴伸展流程
            UILogService.AddLogAndIncreaseIndent($"开始执行{endType}端R轴伸展到{stationType}位置");

            try
            {
                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock获取状态
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Robot状态 (MRS1~MRS3)");
                    if (!CheckRobotStatusForOperation("R轴位置移动"))
                    {
                        UILogService.AddErrorLog("机器人状态不允许执行R轴位置移动操作");
                        UILogService.DecreaseIndentAndAddErrorLog("R轴伸展失败: 机器人状态不允许操作");
                        return (false, "机器人状态不允许执行R轴位置移动操作");
                    }
                    UILogService.AddSuccessLog("Robot状态检查通过");
                }

                // 2. 检查R轴是否已在目标位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"检查{endType}端R轴是否已在{stationType}位置");
                    bool alreadyAtTarget = false;
                    switch (endType)
                    {
                        case EnuRobotEndType.Smooth:
                            if (_interLock.SubsystemStatus.Robot.Status.EnuTAndRAxisSmoothExtendDestination == stationType)
                            {
                                alreadyAtTarget = true;
                                UILogService.AddLog($"Smooth端已在{stationType}位置");
                            }
                            break;

                        case EnuRobotEndType.Nose:
                            if (_interLock.SubsystemStatus.Robot.Status.EnuTAndRAxisNoseExtendDestination == stationType)
                            {
                                alreadyAtTarget = true;
                                UILogService.AddLog($"Nose端已在{stationType}位置");
                            }
                            break;
                    }

                    if (alreadyAtTarget)
                    {
                        UILogService.AddSuccessLog($"{endType}端已在{stationType}位置，无需移动R轴");
                        UILogService.DecreaseIndentAndAddSuccessLog($"R轴伸展完成: 已在目标位置");
                        return (true, $"{endType}端已在{stationType}位置，无需移动R轴");
                    }
                    UILogService.AddLog($"R轴未在{stationType}位置，需要执行伸展操作");
                }

                // 3. Shuttle滑出传感器检查 (SPS11检查)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Shuttle滑出传感器状态 (SPS11)");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                // 4. SLIT DOOR状态检查【ChamberA、ChamberB有Slit Door需要InterLock判断】
                using (UILogService.CreateIndentScope())
                {
                    switch (stationType)
                    {
                        case EnuLocationStationType.ChamberA:
                            UILogService.AddLog("检查ChamberA Slit Door状态");
                            var slitDoorStatus = _interLock.SubsystemStatus.ChamberA.Status.SlitDoorStatus;
                            if (slitDoorStatus != EnuSlitDoorStatus.Open)
                            {
                                UILogService.AddErrorLog("ChamberA Slit Door未打开，请检查");
                                UILogService.DecreaseIndentAndAddErrorLog("R轴伸展失败: ChamberA Slit Door未打开");
                                return (false, "ChamberA Slit Door未打开，请检查");
                            }
                            UILogService.AddSuccessLog("ChamberA Slit Door已打开");
                            break;

                        case EnuLocationStationType.ChamberB:
                            UILogService.AddLog("检查ChamberB Slit Door状态");
                            slitDoorStatus = _interLock.SubsystemStatus.ChamberB.Status.SlitDoorStatus;
                            if (slitDoorStatus != EnuSlitDoorStatus.Open)
                            {
                                UILogService.AddErrorLog("ChamberB Slit Door未打开，请检查");
                                UILogService.DecreaseIndentAndAddErrorLog("R轴伸展失败: ChamberB Slit Door未打开");
                                return (false, "ChamberB Slit Door未打开，请检查");
                            }
                            UILogService.AddSuccessLog("ChamberB Slit Door已打开");
                            break;

                        case EnuLocationStationType.CoolingChamber:
                        case EnuLocationStationType.Cassette:
                            UILogService.AddLog($"{stationType}无需检查Slit Door状态");
                            break;
                    }
                }

                // 5. T轴对准站点检查【判断T轴是否对准相应的站点stationType】
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"检查T轴是否对准{stationType}站点");
                    bool blHasToLocation = false;

                    switch (endType)
                    {
                        case EnuRobotEndType.Smooth:
                            // 直接比较枚举值，判断Smooth端是否已到目的地
                            blHasToLocation = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status.EnuTAxisSmoothDestination.GetTAxisLocation() == stationType.GetTAxisLocation();
                            if (!blHasToLocation)
                            {
                                UILogService.AddErrorLog($"Smooth端未在{stationType}位置，无法移动R轴");
                                UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展失败: T轴未对准目标站点");
                                return (false, $"Smooth端未在{stationType}位置，准备移动R轴");
                            }
                            UILogService.AddLog($"Smooth端T轴已对准{stationType}位置");
                            break;

                        case EnuRobotEndType.Nose:
                            // 直接比较枚举值，判断Nose端是否已到目的地
                            blHasToLocation = SS200InterLockMain.Instance.SubsystemStatus.Robot.Status.EnuTAxisNoseDestination.GetTAxisLocation() == stationType.GetTAxisLocation();
                            if (!blHasToLocation)
                            {
                                UILogService.AddErrorLog($"Nose端未在{stationType}位置，无法移动R轴");
                                UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展失败: T轴未对准目标站点");
                                return (false, $"Nose端未在{stationType}位置，准备移动R轴");
                            }
                            UILogService.AddLog($"Nose端T轴已对准{stationType}位置");
                            break;
                    }
                    UILogService.AddSuccessLog("T轴对准站点检查通过");
                }

                // 6. Z轴位置检查【判断Z轴是否在相应的站点stationType位置】
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"检查Z轴是否在{stationType}对应的高度位置");

                    // 使用重试机制检查Z轴位置
                    bool zAxisPositionCorrect = await CheckZAxisPositionWithRetry(endType, stationType, cancellationToken);

                    if (!zAxisPositionCorrect)
                    {
                        UILogService.AddErrorLog($"{endType}端的{stationType}位置，Z轴不在对应的高度位置");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展失败: Z轴高度位置不正确");
                        return (false, $"{endType}端的{stationType}位置，不在对应的Z轴位置");
                    }
                    UILogService.AddSuccessLog("Z轴高度位置检查通过");
                }

                // 7. 执行R轴伸展到目标位置 (AR20-RP11)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"执行R轴伸展到{stationType}位置 (AR20-RP11)");
                    int positionRAxisPositionValue = RobotPositionParameters.GetRAxisPosition(endType, stationType);
                    var result = await MoveRAxisToPositionAsync(cmdService, positionRAxisPositionValue, true, true, 10000, cancellationToken);

                    if (!result.Success)
                    {
                        UILogService.AddErrorLog($"R轴伸展失败: {result.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展失败: {result.Message}");
                        return result;
                    }
                    UILogService.AddSuccessLog($"R轴伸展到{stationType}位置成功");
                }

                // 8. 伸展完成后再次检查Shuttle传感器
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("伸展完成后检查Shuttle滑出传感器状态");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"✅ R轴成功伸展到{stationType}位置");
                return (true, $"R轴成功伸展到{stationType}位置");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"R轴伸展异常: {ex.Message}");
                return (false, $"R轴移动异常: {ex.Message}");
            }
        }

        #endregion R轴位置移动

        #region R轴归零

        /// <summary>
        /// R轴归零 - 根据AR19逻辑实现完整的R轴归零流程，嵌入InterLock检查
        /// 主要利用InterLock系统获取状态和配置信息
        /// 实现AR19文档中的完整逻辑：
        /// 1. Robot状态检查 (MRS1~MRS3)
        /// 2. R轴位置状态检查 (RS18 or others position)
        /// 3. 滑出传感器安装检查 (SPS11)
        /// 4. 滑出传感器状态检查 (DI19, DI20)
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> ZeroRAxisAsync(
            this IS200McuCmdService cmdService,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录R轴归零流程
            UILogService.AddLogAndIncreaseIndent("开始执行R轴归零操作(AR19)");

            try
            {
                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock系统
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Robot状态 (MRS1~MRS3)");
                    if (!CheckRobotStatusForOperation("R轴归零"))
                    {
                        UILogService.AddErrorLog("机器人状态不允许执行R轴归零操作");
                        UILogService.DecreaseIndentAndAddErrorLog("R轴归零失败: 机器人状态不允许操作");
                        return (false, "机器人状态不允许执行R轴归零操作");
                    }
                    UILogService.AddSuccessLog("Robot状态检查通过");
                }

                // 2. R轴位置状态检查 (RS18) - 使用InterLock系统
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查R轴位置状态 (RS18)");
                    var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                    if (robotStatus.RAxisIsZeroPosition)
                    {
                        UILogService.AddSuccessLog("R轴已在零位(RS18)，归零完成");
                        UILogService.DecreaseIndentAndAddSuccessLog("R轴归零完成: 已在零位");
                        return (true, "R轴已在零位，归零完成");
                    }
                    UILogService.AddLog("R轴不在零位，需要执行归零流程");
                }

                // 3. 执行AR20-RP18：移动R轴到零位（增强版：等待完成并验证）
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行AR20-RP18，移动R轴到零位");
                    int rAxisZeroPosition = _interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;
                    UILogService.AddLog($"R轴零位步数: {rAxisZeroPosition}");

                    // 使用增强版移动方法：等待移动完成并验证位置
                    var moveResult = await MoveRAxisToPositionAsync(cmdService, rAxisZeroPosition,
                        waitForCompletion: true, verifyPosition: true, timeoutMs: 15000, cancellationToken: cancellationToken);
                    if (!moveResult.Success)
                    {
                        UILogService.AddErrorLog($"R轴移动到零位失败: {moveResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴归零失败: {moveResult.Message}");
                        return moveResult;
                    }
                    UILogService.AddSuccessLog("R轴移动到零位成功");
                }

                // 4. 额外验证：确认R轴确实在零位（RS18状态）
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("最终验证R轴零位状态 (RS18)");

                    // 等待状态表更新并进行多次验证
                    bool isAtZero = false;
                    int maxRetries = 5;

                    for (int retry = 0; retry < maxRetries; retry++)
                    {
                        // 强制更新状态表
                        await ForceUpdateRobotStatusAsync(cancellationToken);

                        // 检查状态表中的零位状态
                        var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                        if (robotStatus.RAxisIsZeroPosition)
                        {
                            isAtZero = true;
                            UILogService.AddSuccessLog($"R轴零位状态验证通过 (第{retry + 1}次检查)");
                            break;
                        }

                        if (retry < maxRetries - 1)
                        {
                            UILogService.AddLog($"第{retry + 1}次检查R轴零位状态未通过，等待状态更新...");
                            await Task.Delay(200, cancellationToken); // 等待200ms让状态表更新
                        }
                    }

                    if (!isAtZero)
                    {
                        // 获取当前位置信息用于诊断
                        var currentPosition = await _interLock.RTZAxisPosition.GetCurrentRTZSteps();
                        int rAxisZeroPos = _interLock.SubsystemConfigure.Robot.RP18_RAxisZeroPosition.Value;
                        string diagMessage = $"R轴零位状态验证失败。当前位置: {currentPosition.RAxisStep}, 零位配置: {rAxisZeroPos}, 差值: {Math.Abs(currentPosition.RAxisStep - rAxisZeroPos)}";

                        UILogService.AddWarningLog(diagMessage);
                        UILogService.AddWarningLog("建议检查：1) 硬件是否实际到位 2) 状态表更新机制 3) 位置容差配置");

                        // 这里不直接返回失败，而是给出警告，因为可能是状态更新延迟
                        UILogService.AddWarningLog("R轴归零命令已执行，但状态表显示未在零位，请检查实际硬件位置");
                    }
                }

                // 4. Shuttle滑出传感器检查 (SPS11检查)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Shuttle滑出传感器状态");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"R轴归零失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                UILogService.DecreaseIndentAndAddSuccessLog("✅ R轴归零操作完成");
                return (true, "R轴归零完成");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"R轴归零异常: {ex.Message}");
                return (false, $"R轴归零异常: {ex.Message}");
            }
        }

        #endregion R轴归零

        #endregion R轴操作

        #region Z轴操作

        #region Z轴基础移动

        /// <summary>
        /// 移动Z轴到指定位置
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="positionValue">位置步数值（RP19-RP28对应的具体步数）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveZAxisToPositionAsync(
            this IS200McuCmdService cmdService,
            int positionValue,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录Z轴基础移动
            UILogService.AddLogAndIncreaseIndent($"执行Z轴移动到{positionValue}步数位置");

            try
            {
                // 1. 安全检查
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行Z轴安全高度检查");
                    int safeZAxisPosition = App.AppIniConfig.ZAxixMinSafeValue;
                    UILogService.AddLog($"Z轴安全警界位置值: {safeZAxisPosition}");

                    if (positionValue < safeZAxisPosition)
                    {
                        UILogService.AddErrorLog($"***安全警告***：Z轴位置值{positionValue}小于安全警界位置值{safeZAxisPosition}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴移动失败: 目标位置低于安全高度");
                        return (false, $"Z轴位置值{positionValue}小于保存的Z轴安全警界位置值{safeZAxisPosition}，请检查参数");
                    }
                    UILogService.AddSuccessLog("Z轴安全高度检查通过");
                }

                // 2. 执行Z轴移动
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"发送Z轴移动命令，目标位置: {positionValue}");
                    var result = await cmdService.ExecuteRobotZAxisCommandAsync(positionValue);

                    if (result.ReturnInfo == 0)
                    {
                        UILogService.AddSuccessLog($"Z轴移动命令执行成功");
                        UILogService.DecreaseIndentAndAddSuccessLog($"Z轴移动到{positionValue}步数位置成功");
                        return (true, $"Z轴移动到{positionValue}步数位置成功");
                    }
                    else
                    {
                        UILogService.AddErrorLog($"Z轴移动命令执行失败: {result.Response}, 错误码: 0x{result.ReturnInfo:X4}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴移动失败: 错误码 0x{result.ReturnInfo:X4}");
                        return (false, $"Z轴移动失败: {result.Response}, 错误码: {result.ReturnInfo}");
                    }
                }
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"Z轴移动异常: {ex.Message}");
                return (false, $"Z轴移动异常: {ex.Message}");
            }
        }

        #endregion Z轴基础移动

        #region Z轴取放片位置移动

        /// <summary>
        /// 根据端口类型和站点类型移动Z轴到取片位置【非Cassette取放片位置一致，依靠钉针上下取放片，估取放片一致】
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="slot">cassette 、其它腔体的Slot号</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveZAxisToGetPositionAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType stationType, int slot,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录Z轴取片位置移动
            UILogService.AddLogAndIncreaseIndent($"开始执行{endType}端Z轴移动到{stationType}取片位置 (Slot: {slot})");

            try
            {
                int positionValue = -1;

                // 1. 计算目标位置
                using (UILogService.CreateIndentScope())
                {
                    if (stationType == EnuLocationStationType.Cassette)
                    {
                        UILogService.AddLog($"计算Cassette Slot {slot}的取片位置");

                        if (slot < Golbal.CassetteSlotMin || slot > Golbal.CassetteSlotMax)
                        {
                            UILogService.AddErrorLog($"Cassette Slot:{slot}超出范围，必须在{Golbal.CassetteSlotMin}~{Golbal.CassetteSlotMax}之间");
                            UILogService.DecreaseIndentAndAddErrorLog($"Z轴取片位置移动失败: Slot号超出范围");
                            throw new Exception($"Cassette Slot:{slot}，必须再{Golbal.CassetteSlotMin}~{Golbal.CassetteSlotMax}之间");
                        }

                        // 获取Z轴基准Pin Search位置值
                        var basePinSearchResultVallue = BasePinSearchResultVallue(cmdService, endType);
                        UILogService.AddLog($"基准Pin Search位置: {basePinSearchResultVallue}");

                        // 按8英寸Wafer计算晶圆盒取片位置
                        var cassetteSlotDeviation = GetCassetteSlotDeviation(EnuWaferSize.Inch8, endType, EnuWaferMethod.Get);
                        positionValue = basePinSearchResultVallue + (slot - 1) * 1270 + cassetteSlotDeviation;
                        UILogService.AddLog($"Z轴取片位置计算: {basePinSearchResultVallue} + ({slot} - 1) * 1270 + {cassetteSlotDeviation} = {positionValue}");
                    }
                    else
                    {
                        UILogService.AddLog($"执行{endType}端到{stationType}位置的InterLock安全检查");

                        // 执行完整的InterLock安全检查
                        /*

                         情景描述：当R轴伸进CoolingTop或者CoolingBottom腔体内，在里面只能调整同一个腔体的Get或者Put高度，不能跨越到另外一个，think harder

                         */

                        var safetyCheckResult = await PerformZAxisMoveInterLockSafetyCheckAsync(endType, stationType, cancellationToken);
                        if (!safetyCheckResult.Success)
                        {
                            UILogService.AddErrorLog($"InterLock安全检查失败: {safetyCheckResult.Message}");
                            UILogService.DecreaseIndentAndAddErrorLog($"Z轴取片位置移动失败: {safetyCheckResult.Message}");
                            return safetyCheckResult;
                        }

                        positionValue = RobotPositionParameters.GetZAxisGetPosition(endType, stationType);
                        UILogService.AddLog($"Z轴取片位置步数: {positionValue} (端口: {endType}, 站点: {stationType})");
                    }
                    UILogService.AddSuccessLog($"目标位置计算完成: {positionValue}");
                }

                // 2. 执行Z轴移动到取片位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"执行Z轴移动到取片位置: {positionValue}");
                    var moveResult = await MoveZAxisToPositionAsync(cmdService, positionValue, cancellationToken);
                    if (!moveResult.Success)
                    {
                        UILogService.AddErrorLog($"Z轴移动到取片位置失败: {moveResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴取片位置移动失败: {moveResult.Message}");
                        return moveResult;
                    }
                    UILogService.AddSuccessLog("Z轴移动到取片位置成功");
                }

                // 3. Shuttle滑出传感器检查 (SPS11检查)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Shuttle滑出传感器状态");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴取片位置移动失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"✅ 成功执行{endType}端Z轴移动到{stationType}取片位置");
                return (true, $"成功执行{endType}端升降到{stationType}位置");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"Z轴取片位置移动异常: {ex.Message}");
                return (false, $"Z轴移动异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据端口类型和站点类型确定目标T轴和Z轴高度状态
        /// </summary>
        /// <param name="endType">端口类型 (Smooth/Nose)</param>
        /// <param name="stationType">站点类型 (ChamberA/ChamberB/CoolingTop/CoolingBottom/Cassette)</param>
        /// <param name="isGetPosition">是否为取片位置 (true=取片, false=放片)</param>
        /// <returns>目标T轴和Z轴高度状态</returns>
        private static EnuTZAxisHeightStatus GetTargetTZAxisHeightStatus(
            EnuRobotEndType endType,
            EnuLocationStationType stationType,
            bool isGetPosition)
        {
            // 根据端口类型、站点类型和操作类型确定目标高度状态
            if (endType == EnuRobotEndType.Smooth)
            {
                // Smooth端
                switch (stationType)
                {
                    case EnuLocationStationType.ChamberA:
                        return EnuTZAxisHeightStatus.SmoothToCHA;

                    case EnuLocationStationType.ChamberB:
                        return EnuTZAxisHeightStatus.SmoothToCHB;

                    case EnuLocationStationType.CoolingTop:
                        return isGetPosition ? EnuTZAxisHeightStatus.SmoothToCTGet : EnuTZAxisHeightStatus.SmoothToCTPut;

                    case EnuLocationStationType.CoolingBottom:
                        return isGetPosition ? EnuTZAxisHeightStatus.SmoothToCBGet : EnuTZAxisHeightStatus.SmoothToCBPut;

                    default:
                        return EnuTZAxisHeightStatus.None;
                }
            }
            else
            {
                // Nose端
                switch (stationType)
                {
                    case EnuLocationStationType.ChamberA:
                        return EnuTZAxisHeightStatus.NoseToCHA;

                    case EnuLocationStationType.ChamberB:
                        return EnuTZAxisHeightStatus.NoseToCHB;

                    case EnuLocationStationType.CoolingTop:
                        return isGetPosition ? EnuTZAxisHeightStatus.NoseToCTGet : EnuTZAxisHeightStatus.NoseToCTPut;

                    case EnuLocationStationType.CoolingBottom:
                        return isGetPosition ? EnuTZAxisHeightStatus.NoseToCBGet : EnuTZAxisHeightStatus.NoseToCBPut;

                    default:
                        return EnuTZAxisHeightStatus.None;
                }
            }
        }

        /// <summary>
        /// 执行Z轴移动的InterLock安全检查 - 根据AR21~AR28文档逻辑实现
        /// 包含以下检查：
        /// 1. Robot状态检查 (MRS1~MRS3)
        /// 2. 滑出传感器安装检查 (SPS11)
        /// 3. 滑出传感器状态检查 (DI19, DI20)
        /// 4. T轴位置状态检查 (RS1, RS2, RS5等)
        /// 5. 腔室压力检查 (RPS29)
        /// 6. 装载锁压力检查 (SP13/SP14)
        /// </summary>
        /// <param name="endType">端口类型 (Smooth/Nose)</param>
        /// <param name="stationType">站点类型 (ChamberA/ChamberB/CoolingTop/CoolingBottom/Cassette)</param>
        /// <returns>安全检查结果</returns>
        private static async Task<(bool Success, string Message)> PerformZAxisMoveInterLockSafetyCheckAsync(
            EnuRobotEndType endType,
            EnuLocationStationType stationType,
            CancellationToken cancellationToken)
        {
            try
            {
                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock获取状态
                UILogService.AddLog("步骤1: 检查Robot状态 (MRS1~MRS3)...");
                if (!CheckRobotStatusForOperation($"机器人{endType}端升降到{stationType}位置"))
                {
                    return (false, $"机器人状态不允许执行{endType}端升降到{stationType}位置操作");
                }

                // 额外判断是否Z轴已经升降到指定位置，已经在指定位置则不需要Z轴升降，直接返回成功
                var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                var currentTZAxisHeightStatus = robotStatus.EnuTAndZAxisHeightStatus;

                // 根据当前方法名判断是取片还是放片操作
                // 注意：这里我们无法直接获取调用方法的信息，所以需要根据上下文判断
                // 在实际应用中，应该将isGetPosition作为参数传入
                bool isGetPosition = true; // 默认为取片操作

                // 根据端口类型和站点类型确定目标Z轴高度状态
                EnuTZAxisHeightStatus targetHeightStatus = GetTargetTZAxisHeightStatus(endType, stationType, isGetPosition);

                // 检查当前Z轴高度是否已经在目标位置
                if (currentTZAxisHeightStatus == targetHeightStatus)
                {
                    string operationType = isGetPosition ? "取片" : "放片";
                    UILogService.AddSuccessLog($"{endType}端Z轴已在{stationType}的{operationType}位置，无需移动");
                    return (true, $"{endType}端Z轴已在{stationType}的{operationType}位置，无需移动");
                }

                //2、Shuttle滑出传感器检查 (SPS11检查) - 使用InterLock获取传感器状态
                var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                if (!shuttleSensorCheck.Success)
                {
                    UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                    return shuttleSensorCheck;
                }

                /*// 2. 滑出传感器安装检查 (SPS11) - 使用InterLock系统
                UILogService.AddLog("步骤2: 检查滑出传感器安装状态 (SPS11)...");
                bool sps11Enable = _interLock.SubsystemConfigure.Shuttle.SPS11_SlideOutBackSensorEnable.BoolValue;
                UILogService.AddLog($"SPS11后滑出传感器使能状态: {sps11Enable}");

                // 3. 根据SPS11状态执行不同的检查流程
                if (sps11Enable)
                {
                    // SPS11=Y: 需要先检查滑出传感器状态 (DI19, DI20)
                    UILogService.AddLog("步骤3: SPS11=Y，检查滑出传感器状态 (DI19, DI20)...");
                    bool di19Status = _interLock.IOInterface.Shuttle.SDI19_WaferSlideOutSensor3BL.Value;
                    bool di20Status = _interLock.IOInterface.Shuttle.SDI20_WaferSlideOutSensor4BR.Value;

                    UILogService.AddLog($"滑出传感器状态: DI19={di19Status}, DI20={di20Status}");

                    // 只有DI19=0 DI20=0时才允许继续
                    if (di19Status || di20Status)
                    {
                        // 根据传感器状态返回相应的报警
                        if (!di19Status && di20Status)
                        {
                            // DI19=0 DI20=1: RA20 ALARM
                            string alarmMessage = _interLock.AlarmCode.Robot.RA20_BRSlideOutDetected.Content;
                            string alarmChsMessage = _interLock.AlarmCode.Robot.RA20_BRSlideOutDetected.ChsContent;
                            UILogService.AddErrorLog($"RA20报警: {alarmMessage} ({alarmChsMessage})");
                            return Task.FromResult((false, $"RA20报警: {alarmChsMessage}"));
                        }
                        else if (di19Status && !di20Status)
                        {
                            // DI19=1 DI20=0: RA19 ALARM
                            string alarmMessage = _interLock.AlarmCode.Robot.RA19_BLSlideOutDetected.Content;
                            string alarmChsMessage = _interLock.AlarmCode.Robot.RA19_BLSlideOutDetected.ChsContent;
                            UILogService.AddErrorLog($"RA19报警: {alarmMessage} ({alarmChsMessage})");
                            return Task.FromResult((false, $"RA19报警: {alarmChsMessage}"));
                        }
                        else
                        {
                            // DI19=1 DI20=1: RA21 ALARM
                            string alarmMessage = _interLock.AlarmCode.Robot.RA21_BLAndBRSlideOutDetected.Content;
                            string alarmChsMessage = _interLock.AlarmCode.Robot.RA21_BLAndBRSlideOutDetected.ChsContent;
                            UILogService.AddErrorLog($"RA21报警: {alarmMessage} ({alarmChsMessage})");
                            return Task.FromResult((false, $"RA21报警: {alarmChsMessage}"));
                        }
                    }
                }*/

                // 4. T轴位置状态检查 - 根据端口类型和站点类型检查T轴是否在正确位置
                UILogService.AddLog("步骤4: 检查T轴位置状态...");

                // 根据端口类型和站点类型确定需要检查的T轴位置
                bool isTAxisInCorrectPosition = await CheckTAxisPositionForZAxisMove(endType, stationType, cancellationToken);
                if (!isTAxisInCorrectPosition)
                {
                    // T轴不在正确位置: RA12 ALARM
                    string alarmMessage = _interLock.AlarmCode.Robot.RA12_TAxisPositionZAxisError.Content;
                    string alarmChsMessage = _interLock.AlarmCode.Robot.RA12_TAxisPositionZAxisError.ChsContent;
                    UILogService.AddErrorLog($"RA12报警: {alarmMessage} ({alarmChsMessage})");
                    return (false, $"RA12报警: {alarmChsMessage}");
                }

                // 5. 腔室压力检查 (RPS29) - 仅对腔室站点进行检查
                if (stationType == EnuLocationStationType.ChamberA || stationType == EnuLocationStationType.ChamberB)
                {
                    UILogService.AddLog("步骤5: 检查腔室压力状态 (RPS29)...");
                    bool rps29Enable = _interLock.SubsystemConfigure.Robot.RPS29_ChamberPressureCheck.BoolValue;
                    UILogService.AddLog($"RPS29腔室压力检查使能状态: {rps29Enable}");

                    if (rps29Enable)
                    {
                        // 6. 装载锁压力检查 (SP13/SP14) - 根据腔室压力状态确定Z轴高度
                        UILogService.AddLog("步骤6: 检查装载锁压力状态 (SP13/SP14)...");

                        // 获取装载锁真空状态 - 使用Chamber子系统状态
                        var chamberStatus = stationType == EnuLocationStationType.ChamberA
                            ? _interLock.SubsystemStatus.ChamberA.Status
                            : _interLock.SubsystemStatus.ChamberB.Status;

                        var loadlockVacuumStatus = chamberStatus.LoadlockVacuumStatus;
                        UILogService.AddLog($"装载锁真空状态: {loadlockVacuumStatus}");

                        // 根据装载锁真空状态确定Z轴高度调整
                        if (loadlockVacuumStatus == EnuLoadlockVacuumStatus.PressureVacuum)
                        {
                            // SP13: 装载锁有真空，需要调整Z轴高度
                            UILogService.AddLog("SP13: 装载锁有真空，将调整Z轴高度");
                        }
                        else
                        {
                            // SP14: 装载锁无真空，使用标准Z轴高度
                            UILogService.AddLog("SP14: 装载锁无真空，使用标准Z轴高度");
                        }
                    }
                }

                UILogService.AddSuccessLog("Z轴移动InterLock安全检查通过");
                return (true, "安全检查通过");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"Z轴移动InterLock安全检查异常: {ex.Message}");
                return (false, $"安全检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查Z轴是否在正确位置用于R轴移动
        /// 包含多次延迟重试机制：当Z轴位置检查返回false时，可能是因为状态表还没有更新到最新位置，
        /// 会进行最多5次重试，在最后一次重试前会弹出对话框确认
        /// </summary>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>Z轴是否在正确位置</returns>
        private static async Task<bool> CheckZAxisPositionWithRetry(EnuRobotEndType endType, EnuLocationStationType stationType, CancellationToken cancellationToken)
        {
            // 记录输入参数
            UILogService.AddLog($"检查Z轴高度位置 - 端口类型: {endType}, 站点类型: {stationType}");

            const int maxRetries = 5;
            const int delayMs = 1000;

            // 初始检查（不是重试）
            bool firstCheckResult = PerformZAxisPositionCheck(endType, stationType);

            if (firstCheckResult)
            {
                UILogService.AddSuccessLog($"Z轴高度位置检查通过 (初始检查成功)");
                return true;
            }

            // 初始检查失败，开始重试流程
            UILogService.AddWarningLog($"Z轴高度位置检查失败 (初始检查)，可能状态表还未更新，开始重试流程...");

            try
            {
                // 进行重试（第1次到第4次重试）
                for (int retryCount = 1; retryCount < maxRetries; retryCount++)
                {
                    // 在最后一次重试前弹出确认对话框
                    if (retryCount == maxRetries - 1)
                    {
                        UILogService.AddWarningLog($"即将进行最后一次Z轴高度位置检查重试 (第{retryCount}次重试)");

                        // 弹出确认对话框
                        var dialogResult = MessageBox.Show(
                            $"Z轴高度位置检查已重试{retryCount - 1}次仍未通过。\n\n" +
                            $"端口类型: {endType}\n" +
                            $"站点类型: {stationType}\n\n" +
                            $"是否继续进行最后一次重试？\n\n" +
                            $"点击'是'继续重试，点击'否'直接返回失败。",
                            "Z轴高度位置检查重试确认",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (dialogResult == MessageBoxResult.No)
                        {
                            UILogService.AddWarningLog($"用户取消最后一次重试，Z轴高度位置检查失败");
                            return false;
                        }

                        UILogService.AddLog($"用户确认继续最后一次重试");
                    }

                    // 延迟等待状态表更新
                    UILogService.AddLog($"延迟{delayMs}ms等待状态表更新 (第{retryCount}次重试)");
                    await Task.Delay(delayMs, cancellationToken);

                    // 执行重试检查
                    bool retryCheckResult = PerformZAxisPositionCheck(endType, stationType);

                    if (retryCheckResult)
                    {
                        UILogService.AddSuccessLog($"Z轴高度位置检查通过 (第{retryCount}次重试成功)");
                        return true;
                    }
                    else
                    {
                        UILogService.AddWarningLog($"Z轴高度位置检查失败 (第{retryCount}次重试) - 端口: {endType}, 站点: {stationType}");
                    }
                }

                // 所有重试都失败
                UILogService.AddErrorLog($"Z轴高度位置检查失败 (初始检查+{maxRetries - 1}次重试均失败) - 端口: {endType}, 站点: {stationType}");
                return false;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"Z轴高度位置检查重试过程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查T轴是否在正确位置用于Z轴移动
        /// 包含多次延迟重试机制：当T轴位置检查返回false时，可能是因为状态表还没有更新到最新位置，
        /// 会进行最多5次重试，在最后一次重试前会弹出对话框确认
        /// </summary>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>T轴是否在正确位置</returns>
        private static async Task<bool> CheckTAxisPositionForZAxisMove(EnuRobotEndType endType, EnuLocationStationType stationType, CancellationToken cancellationToken)
        {
            // 记录输入参数
            UILogService.AddLog($"检查T轴位置 - 端口类型: {endType}, 站点类型: {stationType}");

            const int maxRetries = 5;
            const int delayMs = 1000;

            // 初始检查（不是重试）
            bool firstCheckResult = PerformTAxisPositionCheck(endType, stationType);

            if (firstCheckResult)
            {
                UILogService.AddSuccessLog($"T轴位置检查通过 (初始检查成功)");
                return true;
            }

            // 初始检查失败，开始重试流程
            UILogService.AddWarningLog($"T轴位置检查失败 (初始检查)，可能状态表还未更新，开始重试流程...");

            try
            {
                // 进行重试（第1次到第4次重试）
                for (int retryCount = 1; retryCount < maxRetries; retryCount++)
                {
                    // 在最后一次重试前弹出确认对话框
                    if (retryCount == maxRetries - 1)
                    {
                        UILogService.AddWarningLog($"即将进行最后一次T轴位置检查重试 (第{retryCount}次重试)");

                        // 弹出确认对话框
                        var dialogResult = MessageBox.Show(
                            $"T轴位置检查已重试{retryCount - 1}次仍未通过。\n\n" +
                            $"端口类型: {endType}\n" +
                            $"站点类型: {stationType}\n\n" +
                            $"是否继续进行最后一次重试？\n\n" +
                            $"点击'是'继续重试，点击'否'直接返回失败。",
                            "T轴位置检查重试确认",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Question);

                        if (dialogResult == MessageBoxResult.No)
                        {
                            UILogService.AddWarningLog($"用户取消最后一次重试，T轴位置检查失败");
                            return false;
                        }

                        UILogService.AddLog($"用户确认继续最后一次重试");
                    }

                    // 延迟等待状态表更新
                    UILogService.AddLog($"延迟{delayMs}ms等待状态表更新 (第{retryCount}次重试)");
                    await Task.Delay(delayMs, cancellationToken);

                    // 执行重试检查
                    bool retryCheckResult = PerformTAxisPositionCheck(endType, stationType);

                    if (retryCheckResult)
                    {
                        UILogService.AddSuccessLog($"T轴位置检查通过 (第{retryCount}次重试成功)");
                        return true;
                    }
                    else
                    {
                        UILogService.AddWarningLog($"T轴位置检查失败 (第{retryCount}次重试) - 端口: {endType}, 站点: {stationType}");
                    }
                }

                // 所有重试都失败
                UILogService.AddErrorLog($"T轴位置检查失败 (初始检查+{maxRetries - 1}次重试均失败) - 端口: {endType}, 站点: {stationType}");
                return false;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"T轴位置检查重试过程异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行Z轴位置检查的核心逻辑
        /// </summary>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>Z轴是否在正确位置</returns>
        private static bool PerformZAxisPositionCheck(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            try
            {
                var tAndZAxisHeightStatus = _interLock.SubsystemStatus.Robot.Status.EnuTAndZAxisHeightStatus;

                // 记录当前状态用于调试
                UILogService.AddLog($"当前T和Z轴高度状态: {tAndZAxisHeightStatus}");

                bool zAxisPositionCorrect = false;
                switch (endType)
                {
                    case EnuRobotEndType.Smooth:
                        switch (stationType)
                        {
                            case EnuLocationStationType.ChamberA:
                                zAxisPositionCorrect = tAndZAxisHeightStatus == EnuTZAxisHeightStatus.SmoothToCHA;
                                break;

                            case EnuLocationStationType.ChamberB:
                                zAxisPositionCorrect = tAndZAxisHeightStatus == EnuTZAxisHeightStatus.SmoothToCHB;
                                break;

                            case EnuLocationStationType.Cassette:
                                // 判断Smooth端的Z轴位置是否对应某个Cassette位置
                                zAxisPositionCorrect = true; // 暂时设置为true，后续根据实际情况调整
                                break;

                            case EnuLocationStationType.CoolingChamber:
                            case EnuLocationStationType.CoolingTop:
                            case EnuLocationStationType.CoolingBottom:
                                zAxisPositionCorrect = (tAndZAxisHeightStatus == EnuTZAxisHeightStatus.SmoothToCTGet ||
                                                      tAndZAxisHeightStatus == EnuTZAxisHeightStatus.SmoothToCTPut ||
                                                      tAndZAxisHeightStatus == EnuTZAxisHeightStatus.SmoothToCBGet ||
                                                      tAndZAxisHeightStatus == EnuTZAxisHeightStatus.SmoothToCBPut);
                                break;
                        }
                        break;

                    case EnuRobotEndType.Nose:
                        switch (stationType)
                        {
                            case EnuLocationStationType.ChamberA:
                                zAxisPositionCorrect = tAndZAxisHeightStatus == EnuTZAxisHeightStatus.NoseToCHA;
                                break;

                            case EnuLocationStationType.ChamberB:
                                zAxisPositionCorrect = tAndZAxisHeightStatus == EnuTZAxisHeightStatus.NoseToCHB;
                                break;

                            case EnuLocationStationType.Cassette:
                                // 判断Nose端的Z轴位置是否对应某个Cassette位置
                                zAxisPositionCorrect = true; // 暂时设置为true，后续根据实际情况调整
                                break;

                            case EnuLocationStationType.CoolingChamber:
                            case EnuLocationStationType.CoolingTop:
                            case EnuLocationStationType.CoolingBottom:
                                zAxisPositionCorrect = (tAndZAxisHeightStatus == EnuTZAxisHeightStatus.NoseToCTGet ||
                                                      tAndZAxisHeightStatus == EnuTZAxisHeightStatus.NoseToCTPut ||
                                                      tAndZAxisHeightStatus == EnuTZAxisHeightStatus.NoseToCBGet ||
                                                      tAndZAxisHeightStatus == EnuTZAxisHeightStatus.NoseToCBPut);
                                break;
                        }
                        break;
                }

                // 记录检查结果用于调试
                UILogService.AddLog($"Z轴位置检查结果: {zAxisPositionCorrect} (期望状态匹配: {endType}端到{stationType})");

                return zAxisPositionCorrect;
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"Z轴位置检查异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行T轴位置检查的核心逻辑
        /// </summary>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>T轴是否在正确位置</returns>
        private static bool PerformTAxisPositionCheck(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            var robotStatus = _interLock.SubsystemStatus.Robot.Status;

            // 根据端口类型和站点类型检查T轴位置
            if (endType == EnuRobotEndType.Smooth)
            {
                var currentSmoothDestination = robotStatus.EnuTAxisSmoothDestination;
                UILogService.AddLog($"当前T轴Smooth端位置: {currentSmoothDestination}");

                switch (stationType)
                {
                    case EnuLocationStationType.ChamberA:
                        // RS1: T轴Smooth端到ChamberA位置
                        return currentSmoothDestination == EnuLocationStationType.ChamberA;

                    case EnuLocationStationType.ChamberB:
                        // RS2: T轴Smooth端到ChamberB位置
                        return currentSmoothDestination == EnuLocationStationType.ChamberB;

                    case EnuLocationStationType.CoolingTop:
                    case EnuLocationStationType.CoolingBottom:
                    case EnuLocationStationType.CoolingChamber:
                        // RS3: T轴Smooth端到冷却腔位置
                        return currentSmoothDestination == EnuLocationStationType.CoolingChamber;

                    case EnuLocationStationType.Cassette:
                        // RS4: T轴Smooth端到晶圆盒位置
                        return currentSmoothDestination == EnuLocationStationType.Cassette;

                    default:
                        UILogService.AddErrorLog($"不支持的Smooth端站点类型: {stationType}");
                        return false;
                }
            }
            else // Nose端
            {
                var currentNoseDestination = robotStatus.EnuTAxisNoseDestination;
                UILogService.AddLog($"当前T轴Nose端位置: {currentNoseDestination}");

                switch (stationType)
                {
                    case EnuLocationStationType.ChamberA:
                        // RS5: T轴Nose端到ChamberA位置
                        return currentNoseDestination == EnuLocationStationType.ChamberA;

                    case EnuLocationStationType.ChamberB:
                        // RS6: T轴Nose端到ChamberB位置
                        return currentNoseDestination == EnuLocationStationType.ChamberB;

                    case EnuLocationStationType.CoolingTop:
                    case EnuLocationStationType.CoolingBottom:
                    case EnuLocationStationType.CoolingChamber:
                        // RS7: T轴Nose端到冷却腔位置
                        return currentNoseDestination == EnuLocationStationType.CoolingChamber;

                    case EnuLocationStationType.Cassette:
                        // RS8: T轴Nose端到晶圆盒位置
                        return currentNoseDestination == EnuLocationStationType.Cassette;

                    default:
                        UILogService.AddErrorLog($"不支持的Nose端站点类型: {stationType}");
                        return false;
                }
            }
        }

        /// <summary>
        /// 执行Z轴放片位置移动的InterLock安全检查 - 根据AR29~AR32文档逻辑实现
        /// 包含以下检查：
        /// 1. Robot状态检查 (MRS1~MRS3)
        /// 2. 滑出传感器安装检查 (SPS11)
        /// 3. 滑出传感器状态检查 (DI19, DI20)
        /// 4. T轴位置状态检查 (RS3等)
        /// 5. Z轴高度偏差检查 (RS29)
        /// 6. 装载锁压力检查 (SP13/SP14)
        /// </summary>
        /// <param name="endType">端口类型 (Smooth/Nose)</param>
        /// <param name="stationType">站点类型 (ChamberA/ChamberB/CoolingTop/CoolingBottom/Cassette)</param>
        /// <returns>安全检查结果</returns>
        private static async Task<(bool Success, string Message)> PerformZAxisPutPositionInterLockSafetyCheckAsync(
            EnuRobotEndType endType,
            EnuLocationStationType stationType,
            CancellationToken cancellationToken)
        {
            try
            {
                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock获取状态
                UILogService.AddLog("步骤1: 检查Robot状态 (MRS1~MRS3)...");
                if (!CheckRobotStatusForOperation($"机器人{endType}端升降到{stationType}放片位置"))
                {
                    return (false, $"机器人状态不允许执行{endType}端升降到{stationType}放片位置操作");
                }

                // 额外判断是否Z轴已经升降到指定放片位置，已经在指定位置则不需要Z轴升降，直接返回成功
                var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                var currentTZAxisHeightStatus = robotStatus.EnuTAndZAxisHeightStatus;

                // 根据端口类型和站点类型确定目标Z轴高度状态（放片位置）
                EnuTZAxisHeightStatus targetHeightStatus = GetTargetTZAxisHeightStatus(endType, stationType, false); // false表示放片

                // 检查当前Z轴高度是否已经在目标放片位置
                if (currentTZAxisHeightStatus == targetHeightStatus)
                {
                    UILogService.AddSuccessLog($"{endType}端Z轴已在{stationType}的放片位置，无需移动");
                    return (true, $"{endType}端Z轴已在{stationType}的放片位置，无需移动");
                }

                // 2. Shuttle滑出传感器检查 (SPS11检查) - 使用现有的CheckShuttleSlideOutSensors方法
                UILogService.AddLog("步骤2: 检查Shuttle滑出传感器状态...");
                var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                if (!shuttleSensorCheck.Success)
                {
                    UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                    return shuttleSensorCheck;
                }

                // 3. T轴位置状态检查 - 根据端口类型和站点类型检查T轴是否在正确位置
                UILogService.AddLog("步骤3: 检查T轴位置状态...");

                // 根据端口类型和站点类型确定需要检查的T轴位置
                bool isTAxisInCorrectPosition = await CheckTAxisPositionForZAxisMove(endType, stationType, cancellationToken);
                if (!isTAxisInCorrectPosition)
                {
                    // T轴不在正确位置: RA12 ALARM
                    string alarmMessage = _interLock.AlarmCode.Robot.RA12_TAxisPositionZAxisError.Content;
                    string alarmChsMessage = _interLock.AlarmCode.Robot.RA12_TAxisPositionZAxisError.ChsContent;
                    UILogService.AddErrorLog($"RA12报警: {alarmMessage} ({alarmChsMessage})");
                    return (false, $"RA12报警: {alarmChsMessage}");
                }

                UILogService.AddSuccessLog("Z轴放片位置移动InterLock安全检查通过");
                return (true, "安全检查通过");
            }
            catch (Exception ex)
            {
                UILogService.AddErrorLog($"Z轴放片位置移动InterLock安全检查异常: {ex.Message}");
                return (false, $"安全检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 移动Z轴到放片位置
        /// 通过Z轴上升完成晶圆的释放操作
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveZAxisToPutPositionAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType stationType,
            CancellationToken cancellationToken)
        {
            try
            {
                // 调用带slot参数的完整方法
                return await MoveZAxisToPutPositionAsync(cmdService, endType, stationType, 0, cancellationToken);
            }
            catch (Exception ex)
            {
                return (false, $"Z轴移动异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据端口类型和站点类型移动Z轴到放片位置【非Cassette取放片位置一致，依靠钉针上下取放片，估取放片一致】
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="slot">cassette 、其它腔体的Slot号</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> MoveZAxisToPutPositionAsync(
            this IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            EnuLocationStationType stationType, int slot,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录Z轴放片位置移动
            UILogService.AddLogAndIncreaseIndent($"开始执行{endType}端Z轴移动到{stationType}放片位置 (Slot: {slot})");

            try
            {
                int positionValue = -1;

                // 1. 计算目标位置
                using (UILogService.CreateIndentScope())
                {
                    if (stationType == EnuLocationStationType.Cassette)
                    {
                        UILogService.AddLog($"计算Cassette Slot {slot}的放片位置");

                        if (slot < Golbal.CassetteSlotMin || slot > Golbal.CassetteSlotMax)
                        {
                            UILogService.AddErrorLog($"Cassette Slot:{slot}超出范围，必须在{Golbal.CassetteSlotMin}~{Golbal.CassetteSlotMax}之间");
                            UILogService.DecreaseIndentAndAddErrorLog($"Z轴放片位置移动失败: Slot号超出范围");
                            throw new Exception($"Cassette Slot:{slot}，必须再{Golbal.CassetteSlotMin}~{Golbal.CassetteSlotMax}之间");
                        }

                        // 获取Z轴基准Pin Search位置值
                        var basePinSearchResultVallue = BasePinSearchResultVallue(cmdService, endType);
                        UILogService.AddLog($"基准Pin Search位置: {basePinSearchResultVallue}");

                        // 按8英寸Wafer计算晶圆盒放片位置
                        var cassetteSlotDeviation = GetCassetteSlotDeviation(EnuWaferSize.Inch8, endType, EnuWaferMethod.Put);
                        positionValue = basePinSearchResultVallue + (slot - 1) * 1270 + cassetteSlotDeviation;
                        UILogService.AddLog($"Z轴放片位置计算: {basePinSearchResultVallue} + ({slot} - 1) * 1270 + {cassetteSlotDeviation} = {positionValue}");
                    }
                    else
                    {
                        UILogService.AddLog($"执行{endType}端到{stationType}放片位置的InterLock安全检查");

                        // 执行完整的InterLock安全检查
                        var safetyCheckResult = await PerformZAxisPutPositionInterLockSafetyCheckAsync(endType, stationType, cancellationToken);
                        if (!safetyCheckResult.Success)
                        {
                            UILogService.AddErrorLog($"InterLock安全检查失败: {safetyCheckResult.Message}");
                            UILogService.DecreaseIndentAndAddErrorLog($"Z轴放片位置移动失败: {safetyCheckResult.Message}");
                            return safetyCheckResult;
                        }

                        positionValue = RobotPositionParameters.GetZAxisGetPosition(endType, stationType);
                        UILogService.AddLog($"基础Z轴放片位置步数: {positionValue} (端口: {endType}, 站点: {stationType})");

                        switch (stationType)
                        {
                            case EnuLocationStationType.ChamberA:
                            case EnuLocationStationType.ChamberB:
                                UILogService.AddLog($"{stationType}使用基础放片位置，无需补偿");
                                break;

                            case EnuLocationStationType.CoolingTop:
                            case EnuLocationStationType.CoolingBottom:
                                var coolingDelta = RobotConfigureSettings.GetCoolingChamberZDelta();
                                positionValue += coolingDelta; // 冷却腔室Z轴放片位置补偿高度
                                UILogService.AddLog($"冷却腔室放片位置补偿: +{coolingDelta} = {positionValue}");
                                break;

                            case EnuLocationStationType.Cassette:
                                // 晶圆盒取片高度需要根据插槽号动态计算
                                UILogService.AddErrorLog("晶圆盒放片高度需要通过其他方法计算");
                                UILogService.DecreaseIndentAndAddErrorLog("Z轴放片位置移动失败: 不支持的站点类型");
                                throw new NotSupportedException("晶圆盒取片高度需要通过其他方法计算");
                            default:
                                UILogService.AddErrorLog($"不支持的站点类型: {stationType}");
                                UILogService.DecreaseIndentAndAddErrorLog("Z轴放片位置移动失败: 不支持的站点类型");
                                throw new ArgumentException($"不支持的站点类型: {stationType}");
                        }

                        UILogService.AddLog($"最终Z轴放片位置步数: {positionValue}");
                    }
                    UILogService.AddSuccessLog($"目标位置计算完成: {positionValue}");
                }

                // 2. 执行Z轴移动到放片位置
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog($"执行Z轴移动到放片位置: {positionValue}");
                    var moveResult = await MoveZAxisToPositionAsync(cmdService, positionValue, cancellationToken);
                    if (!moveResult.Success)
                    {
                        UILogService.AddErrorLog($"Z轴移动到放片位置失败: {moveResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴放片位置移动失败: {moveResult.Message}");
                        return moveResult;
                    }
                    UILogService.AddSuccessLog("Z轴移动到放片位置成功");
                }

                // 3. Shuttle滑出传感器检查 (SPS11检查)
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Shuttle滑出传感器状态");
                    var shuttleSensorCheck = CheckShuttleSlideOutSensors();
                    if (!shuttleSensorCheck.Success)
                    {
                        UILogService.AddErrorLog($"Shuttle传感器检查失败: {shuttleSensorCheck.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴放片位置移动失败: {shuttleSensorCheck.Message}");
                        return shuttleSensorCheck;
                    }
                    UILogService.AddSuccessLog("Shuttle传感器检查通过");
                }

                UILogService.DecreaseIndentAndAddSuccessLog($"✅ 成功执行{endType}端Z轴移动到{stationType}放片位置");
                return (true, $"成功执行{endType}端升降到{stationType}位置");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"Z轴放片位置移动异常: {ex.Message}");
                return (false, $"Z轴移动异常: {ex.Message}");
            }
        }

        private static int BasePinSearchResultVallue(IS200McuCmdService cmdService, EnuRobotEndType endType)
        {
            var basePinSearchResultVallue = -1;//RobotPositionParameters.GetZAxisPinSearchPosition();

            switch (endType)
            {
                case EnuRobotEndType.Nose:

                    basePinSearchResultVallue = cmdService.NoseBasePinSearchValue;
                    break;

                case EnuRobotEndType.Smooth:
                    basePinSearchResultVallue = cmdService.SmoothBasePinSearchValue;
                    break;
            }

            if (basePinSearchResultVallue <= 0)//如果小于0，说明获取失败，强制设置PinSearch基准值为：1900步
            {
                basePinSearchResultVallue = Golbal.BasePinSearchResultVallue;
                UILogService.AddErrorLog($"获取{endType}基准Pin Search位置失败，强制设置PinSearch基准值为: {basePinSearchResultVallue}");
            }

            return basePinSearchResultVallue;
        }

        /// <summary>
        /// 根据晶圆尺寸、Robot端口类型和晶圆取放获取Z轴插销补偿高度
        /// </summary>
        /// <returns>Z轴插销补偿高度</returns>
        public static int GetCassetteSlotDeviation(EnuWaferSize enunuWaferSize, EnuRobotEndType endType, EnuWaferMethod enuWaferMethod)
        {
            var deviation = 0;

            var zpin_wafer = RobotConfigureSettings.GetWaferZPinByWaferSize(enunuWaferSize);

            var cassetteZPutDelta = RobotConfigureSettings.GetCassetteZPutDelta(enunuWaferSize);

            switch (enuWaferMethod)
            {
                case EnuWaferMethod.Get:
                    // 取片时，Z轴插销高度为晶圆盒取片位置
                    deviation = zpin_wafer;
                    break;

                case EnuWaferMethod.Put:
                    // 放片时，Z轴插销高度为晶圆盒放片位置
                    deviation = zpin_wafer + cassetteZPutDelta;
                    break;
            }

            return deviation;
        }

        #endregion Z轴取放片位置移动

        #region Z轴归零

        /// <summary>
        /// Z轴归零 - 根据AR33逻辑实现完整的Z轴归零流程，嵌入InterLock检查
        /// 主要利用InterLock系统获取状态和配置信息
        /// 实现AR33文档中的完整逻辑：
        /// 1. Robot状态检查 (MRS1~MRS3)
        /// 2. T轴位置状态检查 (RS9 or others position)
        /// 3. R轴位置状态检查 (RS18 or others position)
        /// 4. 执行Z轴归零 (AR39-RP27)
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>执行结果</returns>
        public static async Task<(bool Success, string Message)> ZeroZAxisAsync(
            this IS200McuCmdService cmdService,
            CancellationToken cancellationToken)
        {
            // 使用层次化日志记录Z轴归零流程
            UILogService.AddLogAndIncreaseIndent("开始执行Z轴归零操作(AR33)");

            try
            {
                // 1. Robot状态检查 (MRS1~MRS3) - 使用InterLock系统
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Robot状态 (MRS1~MRS3)");
                    if (!CheckRobotStatusForOperation("Z轴归零"))
                    {
                        UILogService.AddErrorLog("机器人状态不允许执行Z轴归零操作");
                        UILogService.DecreaseIndentAndAddErrorLog("Z轴归零失败: 机器人状态不允许操作");
                        return (false, "机器人状态不允许执行Z轴归零操作");
                    }
                    UILogService.AddSuccessLog("Robot状态检查通过");
                }

                // 2. T轴位置状态检查 (RS9) - 使用InterLock系统
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查T轴位置状态 (RS9)");
                    var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                    if (!robotStatus.TAxisIsZeroPosition)
                    {
                        // T轴不在零位 - RA11 ALARM
                        string alarmMessage = _interLock.AlarmCode.Robot.RA12_TAxisPositionZAxisError.Content;
                        string alarmChsMessage = _interLock.AlarmCode.Robot.RA12_TAxisPositionZAxisError.ChsContent;
                        UILogService.AddErrorLog($"RA11报警: {alarmMessage} ({alarmChsMessage})");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴归零失败: {alarmChsMessage}");
                        return (false, $"RA11报警: {alarmChsMessage}");
                    }
                    UILogService.AddSuccessLog("T轴在零位，检查通过");
                }

                // 3. R轴位置状态检查 (RS18) - 使用InterLock系统
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查R轴位置状态 (RS18)");
                    var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                    if (!robotStatus.RAxisIsZeroPosition)
                    {
                        // R轴不在零位 - RA12 ALARM
                        string alarmMessage = _interLock.AlarmCode.Robot.RA11_RAxisNotHomeZAxisError.Content;
                        string alarmChsMessage = _interLock.AlarmCode.Robot.RA11_RAxisNotHomeZAxisError.ChsContent;
                        UILogService.AddErrorLog($"RA12报警: {alarmMessage} ({alarmChsMessage})");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴归零失败: {alarmChsMessage}");
                        return (false, $"RA12报警: {alarmChsMessage}");
                    }
                    UILogService.AddSuccessLog("R轴在零位，检查通过");
                }

                // 4. 检查Z轴是否已在零位
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("检查Z轴当前位置状态");
                    var robotStatus = _interLock.SubsystemStatus.Robot.Status;
                    if (robotStatus.ZAxisIsZeroPosition)
                    {
                        UILogService.AddSuccessLog("Z轴已在零位，归零完成");
                        UILogService.DecreaseIndentAndAddSuccessLog("Z轴归零完成: 已在零位");
                        return (true, "Z轴已在零位，归零完成");
                    }
                    UILogService.AddLog("Z轴不在零位，需要执行归零操作");
                }

                // 5. 执行AR39-RP27：移动Z轴到零位
                using (UILogService.CreateIndentScope())
                {
                    UILogService.AddLog("执行AR39-RP27，移动Z轴到零位");
                    int zAxisZeroPosition = _interLock.SubsystemConfigure.Robot.RP27_ZAxisZeroPosition.Value;
                    UILogService.AddLog($"Z轴零位步数: {zAxisZeroPosition}");

                    var moveResult = await MoveZAxisToPositionAsync(cmdService, zAxisZeroPosition, cancellationToken);
                    if (!moveResult.Success)
                    {
                        UILogService.AddErrorLog($"Z轴移动到零位失败: {moveResult.Message}");
                        UILogService.DecreaseIndentAndAddErrorLog($"Z轴归零失败: {moveResult.Message}");
                        return moveResult;
                    }
                    UILogService.AddSuccessLog("Z轴移动到零位成功");
                }

                UILogService.DecreaseIndentAndAddSuccessLog("✅ Z轴归零操作完成");
                return (true, "Z轴归零完成");
            }
            catch (Exception ex)
            {
                UILogService.DecreaseIndentAndAddErrorLog($"Z轴归零异常: {ex.Message}");
                return (false, $"Z轴归零异常: {ex.Message}");
            }
        }

        #endregion Z轴归零

        #endregion Z轴操作

        #endregion 三级命令 - 基础轴控制操作

        #region 参数获取方法

        /// <summary>
        /// 获取Z轴放片位置的步数
        /// </summary>
        /// <param name="endType">端口类型</param>
        /// <param name="stationType">站点类型</param>
        /// <returns>Z轴放片位置的步数</returns>
        private static int GetZAxisPutPosition(EnuRobotEndType endType, EnuLocationStationType stationType)
        {
            // 这里需要根据实际硬件参数来设置各个位置的步数值
            // 以下步数值仅为示例，需要根据实际情况调整
            switch (endType)
            {
                case EnuRobotEndType.Nose:
                    switch (stationType)
                    {
                        case EnuLocationStationType.Cassette:
                            return 5000;  // Nose端向晶圆盒放片的Z轴位置
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            return 4000;  // Nose端向腔室放片的Z轴位置
                        case EnuLocationStationType.CoolingTop:
                            return 4000;  // Nose端向冷却TOP放片的Z轴位置
                        case EnuLocationStationType.CoolingBottom:
                            return 4500;  // Nose端向冷却BOTTOM放片的Z轴位置
                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                case EnuRobotEndType.Smooth:
                    switch (stationType)
                    {
                        case EnuLocationStationType.Cassette:
                            return 5200;  // Smooth端向晶圆盒放片的Z轴位置
                        case EnuLocationStationType.ChamberA:
                        case EnuLocationStationType.ChamberB:
                            return 4200;  // Smooth端向腔室放片的Z轴位置
                        case EnuLocationStationType.CoolingTop:
                            return 4200;  // Smooth端向冷却TOP放片的Z轴位置
                        case EnuLocationStationType.CoolingBottom:
                            return 4700;  // Smooth端向冷却BOTTOM放片的Z轴位置
                        default:
                            throw new ArgumentException($"不支持的站点类型: {stationType}");
                    }

                default:
                    throw new ArgumentException($"不支持的端口类型: {endType}");
            }
        }

        #endregion 参数获取方法

        #region 安全检查方法

        /// <summary>
        /// 执行InterLock安全检查
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>检查结果</returns>
        private static async Task<(bool Success, string Message)> CheckInterLockAsync(
            IS200McuCmdService cmdService,
            EnuLocationStationType stationType,
            CancellationToken cancellationToken)
        {
            try
            {
                // TODO: 实现具体的InterLock检查逻辑
                // 这里应该检查系统安全状态、急停状态、门锁状态等
                await Task.Delay(100, cancellationToken); // 模拟检查时间

                // 示例检查逻辑
                // if (系统处于急停状态) return (false, "系统处于急停状态");
                // if (安全门未关闭) return (false, "安全门未关闭");
                // if (气压不足) return (false, "气压不足");

                return (true, "InterLock安全检查通过");
            }
            catch (Exception ex)
            {
                return (false, $"InterLock检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查指定位置的晶圆存在状态
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="stationType">站点类型</param>
        /// <param name="slotNumber">Slot号</param>
        /// <param name="shouldHaveWafer">期望的晶圆状态（true=应该有晶圆，false=应该无晶圆）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>检查结果</returns>
        private static async Task<(bool Success, string Message)> CheckWaferPresenceAsync(
            IS200McuCmdService cmdService,
            EnuLocationStationType stationType,
            int slotNumber,
            bool shouldHaveWafer,
            CancellationToken cancellationToken)
        {
            try
            {
                // TODO: 实现具体的晶圆检测逻辑
                // 这里应该读取相应的传感器状态
                await Task.Delay(100, cancellationToken); // 模拟检测时间

                // 示例检测逻辑
                bool hasWafer = shouldHaveWafer; // 从传感器读取实际状态

                switch (stationType)
                {
                    case EnuLocationStationType.ChamberA:
                    case EnuLocationStationType.ChamberB:
                        // 读取腔体内晶圆传感器
                        // hasWafer = ReadChamberWaferSensor(stationType);
                        break;

                    case EnuLocationStationType.Cassette:
                        // 读取晶圆盒指定Slot的传感器
                        // hasWafer = ReadCassetteSlotSensor(slotNumber);
                        break;

                    case EnuLocationStationType.CoolingTop:
                    case EnuLocationStationType.CoolingBottom:
                        // 读取冷却腔传感器
                        // hasWafer = ReadCoolingChamberSensor(stationType);
                        break;
                }

                if (hasWafer == shouldHaveWafer)
                {
                    string status = shouldHaveWafer ? "有晶圆" : "无晶圆";
                    return (true, $"{stationType}位置状态正确：{status}");
                }
                else
                {
                    string expected = shouldHaveWafer ? "有晶圆" : "无晶圆";
                    string actual = hasWafer ? "有晶圆" : "无晶圆";
                    return (false, $"{stationType}位置状态不符：期望{expected}，实际{actual}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"晶圆状态检查异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 检查Robot机械臂上的晶圆状态
        /// </summary>
        /// <param name="cmdService">命令服务</param>
        /// <param name="endType">端口类型</param>
        /// <param name="shouldHaveWafer">期望的晶圆状态（true=应该有晶圆，false=应该无晶圆）</param>
        /// <param name="cancellationToken">取消令牌</param>
        /// <returns>检查结果</returns>
        private static async Task<(bool Success, string Message)> CheckRobotArmWaferAsync(
            IS200McuCmdService cmdService,
            EnuRobotEndType endType,
            bool shouldHaveWafer,
            CancellationToken cancellationToken)
        {
            try
            {
                // TODO: 实现具体的Robot机械臂晶圆检测逻辑
                // 这里应该读取Robot机械臂上的晶圆传感器
                await Task.Delay(100, cancellationToken); // 模拟检测时间

                // 示例检测逻辑
                bool hasWafer = shouldHaveWafer; // 从传感器读取实际状态

                // 根据端口类型读取相应的传感器
                switch (endType)
                {
                    case EnuRobotEndType.Nose:
                        // hasWafer = ReadNoseEndWaferSensor();
                        break;

                    case EnuRobotEndType.Smooth:
                        // hasWafer = ReadSmoothEndWaferSensor();
                        break;
                }

                if (hasWafer == shouldHaveWafer)
                {
                    string status = shouldHaveWafer ? "有晶圆" : "无晶圆";
                    return (true, $"Robot {endType}端机械臂状态正确：{status}");
                }
                else
                {
                    string expected = shouldHaveWafer ? "有晶圆" : "无晶圆";
                    string actual = hasWafer ? "有晶圆" : "无晶圆";
                    return (false, $"Robot {endType}端机械臂状态不符：期望{expected}，实际{actual}");
                }
            }
            catch (Exception ex)
            {
                return (false, $"Robot机械臂晶圆状态检查异常: {ex.Message}");
            }
        }

        #endregion 安全检查方法
    }
}